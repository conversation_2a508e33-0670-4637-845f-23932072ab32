import { Component } from '@angular/core';
import { ApplicationStatuses, EarlyRepaymentTypes } from '@e2e/lib/types';
import { Store } from '@ngrx/store';
import {
  selectCurrencies,
  selectDateFormat,
} from '@tenant-management/lib/state';
import { dateRequestFormat } from '@tenant-management/lib/types';
import { format } from 'date-fns';
import { portfolios, selectApplicationStatus } from '../../+state';
import { ApplicationCreditLimitEarlyRepaymentPageActions } from '../../+state/actions';

@Component({
  selector: 'e2e-application-credit-limit-early-repayment-simulation',
  templateUrl:
    './application-credit-limit-early-repayment-simulation.component.html',
  styleUrls: [
    './application-credit-limit-early-repayment-simulation.component.scss',
  ],
})
export class ApplicationCreditLimitEarlyRepaymentSimulationComponent {
  portfolios$ = this.store.select(portfolios);
  getCurrency$ = this.store.select(selectCurrencies);
  getDateFormat$ = this.store.select(selectDateFormat);
  getApplicationStatus$ = this.store.select(selectApplicationStatus);

  repaymentTypes = EarlyRepaymentTypes;
  applicationStatuses = ApplicationStatuses;

  constructor(private store: Store) {}

  openFullRepaymentTable(
    loanId: string | number,
    effectiveDate: any,
    fees: any
  ) {
    const earlyRepayment: any = {
      loanId,
      effectiveDate: format(new Date(effectiveDate as Date), dateRequestFormat),
      fees: [],
    };

    fees.forEach((fee: any, index: any) => {
      const hasFixed = fee.fixedRate !== undefined;

      earlyRepayment.fees.push({
        type: fee.feeType,
        spreadMethod: fee.feeSpreadMethod,
        calcType: fee.calcType,
        minAmount: fee.minAmount,
        maxAmount: fee.maxAmount,
        currency: fee.currency,
        percentageRate: fee.percentageRate ? +fee.percentageRate : '',
        fixedAmount: hasFixed ? fee?.fixedRate : '',
        calculationBase: fee.calculationBase,
      } as any);
    });

    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanFullRepayments(
        earlyRepayment
      )
    );
  }

  openPartialRepaymentTable(
    loanId: string | number,
    effectiveDate: any,
    fees: any,
    amountToRepay: any,
    repaymentDistribution: any
  ) {
    const earlyRepayment: any = {
      loanId,
      effectiveDate: format(new Date(effectiveDate as Date), dateRequestFormat),
      fees: [],
      earlyRepaymentAmount: amountToRepay,
      spreadMethod: repaymentDistribution,
    };
    fees.forEach((fee: any, index: any) => {
      const hasFixed = fee.fixedRate !== undefined;
      earlyRepayment.fees.push({
        type: fee.feeType,
        spreadMethod: fee.feeSpreadMethod,
        calcType: fee.calcType,
        minAmount: fee.minAmount,
        maxAmount: fee.maxAmount,
        currency: fee.currency,
        percentageRate: fee.percentageRate ? +fee.percentageRate : '',
        // fixedAmount: fee.fixedAmount,
        fixedAmount: hasFixed ? fee?.fixedRate : '',
        calculationBase: fee.calculationBase,
      } as any);
    });

    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanPartialRepayments(
        earlyRepayment
      )
    );
  }

  openDebtRepaymentTable(
    amount: string | number,
    effectiveDate: any,
    limitId: any
  ) {
    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanDebtRepayments({
        limitId,
        effectiveDate: format(
          new Date(effectiveDate as Date),
          dateRequestFormat
        ),
        amount,
      })
    );
  }
}
