<ng-container *ngIf="getApplications$ | async as applicationsPage">
  <div class="mb-6 flex justify-between items-center pe-6 min-h-[40px]">
    <div class="flex items-center">
      <mat-icon
        svgIcon="icon-badge-check"
        class="icon-color icon-color-explorer mat-tm-icon mat-tm-icon-size-32 me-2"
      ></mat-icon>

      <div class="title-gradient-primary text-3xl font-bold">
        {{ 'applications.applications' | transloco }}
      </div>
    </div>

    <div class="flex items-center">
      <e2e-print-and-download
        printTitle="applications.applications"
      ></e2e-print-and-download>

      <ng-container
        *ngIf="
          getVisualizationConfigNewApplication$ | async as newApplicationConfig
        "
      >
        <button
          type="button"
          class="h-10 text-sm rounded-md ms-6 min-w-[185px]"
          mat-flat-button
          color="primary"
          [matMenuTriggerFor]="applicationsMenu"
          #trigger="matMenuTrigger"
          *ngIf="
            newApplicationConfig.showNewCreditLimit ||
            newApplicationConfig.showNewDeal
          "
        >
          {{ 'general.buttons.newApplication' | transloco }}
          <mat-icon
            iconPositionEnd
            [ngClass]="{
              'rounded-full bg-white text-orientals rotate-180':
                trigger.menuOpen
            }"
          >
            keyboard_arrow_down
          </mat-icon>
        </button>
        <mat-menu #applicationsMenu="matMenu">
          <button
            *ngIf="newApplicationConfig.showNewCreditLimit"
            mat-menu-item
            (click)="setNewLoanBackUrl('/operations/new-credit-limit/owner')"
          >
            {{ 'general.buttons.newCreditLimit' | transloco }}
          </button>
          <button
            *ngIf="newApplicationConfig.showNewDeal"
            mat-menu-item
            (click)="setNewLoanBackUrl('/operations/new-loan/borrower')"
          >
            {{ 'general.buttons.newDeal' | transloco }}
          </button>
        </mat-menu>
      </ng-container>
    </div>
  </div>

  <div class="operation-table-height flex flex-col">
    <div class="grow">
      <div id="print-section" class="main-card pb-4 shadow-primary mb-4">
        <div class="flex justify-between items-center mb-4">
          <div class="me-6 min-w-[445px]">
            <e2e-search-field
              [activeSearch]="applicationsPage.queryParams.search"
              (searchValue)="setSearch($event)"
            ></e2e-search-field>
          </div>

          <div class="flex items-center">
            <tm-select-table-columns
              [columns]="getApplicationsTableColumns$ | async"
              (updateColumns)="updateColumns($event)"
            ></tm-select-table-columns>

            <button
              mat-flat-button
              class="flex items-center min-w-[120px] text-base bg-orientals-12 text-orientals h-11 rounded-md ms-6"
              (click)="onShowFiltersPanel()"
            >
              <mat-icon class="me-2">filter_list</mat-icon>
              <span>
                {{ 'general.buttons.filter' | transloco }}
              </span>
              <span *ngIf="!!applicationsPage.filtersLength" dir="ltr">
                ({{ applicationsPage.filtersLength }})
              </span>
            </button>
          </div>
        </div>

        <tm-no-results
          *ngIf="
            applicationsPage.hasNoApplications ||
              applicationsPage.hasNoFilterResults;
            else table
          "
          noResultsMessage="applications.noFilterResults"
        ></tm-no-results>

        <ng-template #table>
          <div class="table-wrapper table-wrapper-height-370">
            <table
              *ngIf="
                (getApplicationsTableColumns$ | async)
                  ?.selectedTableColumns as columns
              "
              class="w-full"
              aria-label="Applications table"
              mat-table
              [dataSource]="applicationsPage.applications?.applications!"
              matSort
              [matSortActive]="applicationsPage.queryParams.active"
              [matSortDirection]="applicationsPage.queryParams.direction"
              disableClear
              (matSortChange)="sortApplication($event)"
            >
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef class="w-[80px]">
                  {{ 'applications.tableHeaders.id' | transloco }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let application"
                  class="hover-text-orientals"
                  [matTooltip]="application.id"
                >
                  <div dir="ltr" class="truncate w-[80px]">
                    {{ application.id }}
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="applicationType">
                <th
                  mat-header-cell
                  *matHeaderCellDef
                  mat-sort-header
                  class="min-w-[200px]"
                >
                  {{ 'applications.tableHeaders.applicationType' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application">
                  {{
                    'e2e.operationStatuses.' + application.applicationType
                      | transloco
                  }}
                </td>
              </ng-container>

              <ng-container matColumnDef="borrowerName">
                <th
                  mat-header-cell
                  *matHeaderCellDef
                  mat-sort-header
                  class="w-[200px]"
                >
                  {{ 'applications.tableHeaders.borrowerName' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application">
                  <div
                    class="flex items-center"
                    *ngIf="application.applicant as applicant"
                  >
                    <ng-container
                      *ngIf="applicant.type === borrowerTypes.Personal"
                    >
                      <mat-icon
                        svgIcon="icon-personal"
                        class="icon-personal mat-tm-icon mat-tm-icon-size-25 me-1"
                      ></mat-icon>
                    </ng-container>

                    <ng-container
                      *ngIf="applicant.type === borrowerTypes.Business"
                    >
                      <mat-icon
                        svgIcon="icon-business"
                        class="icon-business mat-tm-icon mat-tm-icon-size-28 me-1"
                      ></mat-icon>
                    </ng-container>
                    <div
                      class="truncate w-[200px]"
                      [matTooltip]="applicant.name"
                    >
                      {{ applicant.name }}
                    </div>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="requestedAmount.value">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ 'applications.tableHeaders.requestedAmount' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application" class="text-right">
                  {{
                    application.requestedAmount.value
                      | currency: application.requestedAmount.currency
                  }}
                </td>
              </ng-container>

              <ng-container matColumnDef="createdOn">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ 'applications.tableHeaders.createdOn' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application">
                  {{
                    application.createdOn | date: applicationsPage.dateFormat
                  }}
                </td>
              </ng-container>

              <ng-container matColumnDef="createdBy">
                <th
                  mat-header-cell
                  *matHeaderCellDef
                  mat-sort-header
                  class="min-w-[150px]"
                >
                  {{ 'applications.tableHeaders.createdBy' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application">
                  {{ application.createdBy }}
                </td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>
                  {{ 'applications.tableHeaders.status' | transloco }}
                </th>
                <td mat-cell *matCellDef="let application">
                  <div class="min-w-[150px] flex items-start gap-1">
                    <div
                      class="operation-status me-1"
                      [ngClass]="
                        'application-status-' + application.status | lowercase
                      "
                    >
                      {{
                        'e2e.operationStatuses.' + application.status
                          | transloco
                      }}
                    </div>
                    <ng-container *ngIf="application.status === 'FAILED'">
                      <button
                        type="button"
                        class="btn-clean"
                        (click)="onSendApplicationRetry(application.id)"
                      >
                        <tm-tooltip
                          class="[&_path]:fill-orientals"
                          *ngIf="application.status === 'FAILED'"
                          icon="icon-retry"
                          iconColorClass=""
                          [escape]="false"
                          [tooltipTemplateMessage]="retryApplication"
                          iconSizeClass="mat-tm-icon-size-18"
                        >
                          <div #retryApplication>
                            <div
                              class=""
                              [ngClass]="{
                                'w-[300px]': application.error,
                                'w-auto': !application.error
                              }"
                            >
                              <ng-container *ngIf="!application.error">
                                {{
                                  'general.buttons.retryApplication' | transloco
                                }}
                              </ng-container>
                            </div>
                          </div>
                        </tm-tooltip>
                      </button>
                      <tm-tooltip
                        *ngIf="application.status === 'FAILED'"
                        [escape]="false"
                        [tooltipTemplateMessage]="repaymentAmountTemplate"
                      >
                        <div #repaymentAmountTemplate>
                          <div
                            class=""
                            [ngClass]="{
                              'w-[300px]': application.error,
                              'w-auto': !application.error
                            }"
                          >
                            <ng-container *ngIf="application.error as error">
                              <ng-container
                                *ngIf="
                                  error.status >= 400 && error.status <= 499
                                "
                              >
                                <div class="mb-2 font-bold">
                                  {{ error.title }}
                                </div>
                                <div>
                                  {{ error.detail }}
                                </div>
                              </ng-container>
                              <ng-container
                                *ngIf="
                                  error.status >= 500 && error.status <= 599
                                "
                              >
                                {{
                                  'general.errors.retryApplication' | transloco
                                }}
                              </ng-container>
                            </ng-container>
                            <ng-container *ngIf="!application.error">
                              {{ 'applications.noErrorMessage' | transloco }}
                            </ng-container>
                          </div>
                        </div>
                      </tm-tooltip>
                    </ng-container>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="view" stickyEnd>
                <th mat-header-cell *matHeaderCellDef class="w-12">
                  {{ 'applications.tableHeaders.view' | transloco }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let application"
                  class="w-12 bg-carbon text-center"
                >
                  <button
                    *ngIf="
                      application.applicationType !==
                        applicationType.MortgageNewDeal &&
                      application.applicationType !==
                        applicationType.MortgageDisbursement
                    "
                    mat-mini-fab
                    (click)="
                      navigateToApplicationDetails(
                        application.id,
                        application.status,
                        application.applicationType
                      )
                    "
                  >
                    <mat-icon
                      svgIcon="icon-view"
                      class="mat-tm-icon-size-16"
                    ></mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="columns; sticky: true"></tr>
              <tr mat-row *matRowDef="let application; columns: columns"></tr>
            </table>
          </div>

          <mat-paginator
            [pageIndex]="applicationsPage.queryParams.page"
            [pageSize]="applicationsPage.queryParams.size"
            [pageSizeOptions]="pageSizeOptions"
            [showFirstLastButtons]="showFirstLastButtons"
            [length]="applicationsPage.applications?.totalItems"
            (page)="pageEvent($event)"
            class="mt-2"
          ></mat-paginator>
        </ng-template>
      </div>
    </div>

    <tm-footer></tm-footer>
  </div>
</ng-container>
