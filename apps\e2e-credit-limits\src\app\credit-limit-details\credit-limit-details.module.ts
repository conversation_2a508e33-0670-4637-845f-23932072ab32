import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { CreditLimitDetailsLayoutComponent } from './components/credit-limit-details-layout/credit-limit-details-layout.component';
import { CreditLimitPreviewComponent } from './components/credit-limit-preview/credit-limit-preview.component';
import { CreditLimitDetailsRoutingModule } from './credit-limit-details-routing.module';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DoughnutChartModule } from '@e2e/lib/doughnut-chart';
import { E2eActionsMenuModule } from '@e2e/lib/e2e-actions-menu';
import { E2eAttachedDocumentsModule } from '@e2e/lib/e2e-attached-documents';
import { E2eBorrowerDetailsModule } from '@e2e/lib/e2e-borrower-details';
import { E2eBorrowerViewModule } from '@e2e/lib/e2e-borrower-view';
import { E2eDebtSimulateModule } from '@e2e/lib/e2e-debt-simulate';
import { E2eEarlyRepaymentLoansModule } from '@e2e/lib/e2e-early-repayment-loans';
import { E2eEarlyRepaymentPortfoliosModule } from '@e2e/lib/e2e-early-repayment-portfolios';
import { E2eEarlyRepaymentTracksModule } from '@e2e/lib/e2e-early-repayment-tracks';
import { E2eIdentityPartiesExistingApplicationModule } from '@e2e/lib/e2e-identity-parties-existing-application';
import { E2eLayoutModule } from '@e2e/lib/e2e-layout';
import { E2eLifeInsuranceModule } from '@e2e/lib/e2e-life-insurance';
import { E2eLoanChangeAmountFormModule } from '@e2e/lib/e2e-loan-change-amount-form';
import { E2eLoanChangeDateFormModule } from '@e2e/lib/e2e-loan-change-date-form';
import { E2eLoanDisbursementFormModule } from '@e2e/lib/e2e-loan-disbursement-form';
import { E2eLoanEffectiveDateFormModule } from '@e2e/lib/e2e-loan-effective-date-form';
import { E2eLoanUploadDocumentsModule } from '@e2e/lib/e2e-loan-upload-documents';
import { E2ePartiesViewModule } from '@e2e/lib/e2e-parties-view';
import { E2ePipesModule } from '@e2e/lib/e2e-pipes';
import { E2ePortfoliosDetailsLayoutModule } from '@e2e/lib/e2e-portfolios-details-layout';
import { E2ePortfoliosSortModule } from '@e2e/lib/e2e-portfolios-sort';
import { E2eProductViewModule } from '@e2e/lib/e2e-product-view';
import { FiltersPanelModule } from '@e2e/lib/filters-panel';
import { E2eOverdueTooltipModule } from '@e2e/lib/overdue-tooltip';
import { PrintAndDownloadModule } from '@e2e/lib/print-and-download';
import { SearchFieldModule } from '@e2e/lib/search-field';
import {
  e2eLoanStateEffects,
  e2eLoanStateReducers,
  initialState as e2eLoanInitialState,
} from '@e2e/lib/state';
import { TranslocoModule, TRANSLOCO_SCOPE } from '@ngneat/transloco';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { AngularMaterialModule } from '@tenant-management/lib/angular-material';
import { BackToPreviousPageModule } from '@tenant-management/lib/back-to-previous-page';
import { FooterModule } from '@tenant-management/lib/footer';
import { LoaderModule } from '@tenant-management/lib/loader';
import { NgxDocumentViewerModule } from '@tenant-management/lib/ngx-document-viewer';
import { TooltipModule } from '@tenant-management/lib/tooltip';
import { TmStateSlices } from '@tenant-management/lib/types';
import { UploadDocumentsModule } from '@tenant-management/lib/upload-documents';
import { NgxMaskDirective, NgxMaskPipe } from 'ngx-mask';
import { CreditLimitDetailsEffects } from './+state/credit-limit-details.effect';
import {
  creditLimitDetailsReducer,
  initialState,
} from './+state/credit-limit-details.reducer';
import { CreditLimitActivityLogComponent } from './components/credit-limit-activity-log/credit-limit-activity-log.component';
import { CreditLimitChangeAmountComponent } from './components/credit-limit-change-amount/credit-limit-change-amount.component';
import { CreditLimitCollateralsOwnerDialogComponent } from './components/credit-limit-collaterals-owner-dialog/credit-limit-collaterals-owner-dialog.component';
import { CreditLimitCollateralsComponent } from './components/credit-limit-collaterals/credit-limit-collaterals.component';
import { CreditLimitConsumersComponent } from './components/credit-limit-consumers/credit-limit-consumers.component';
import { CreditLimitDetailsComponent } from './components/credit-limit-details/credit-limit-details.component';
import { CreditLimitEarlyRepaymentComponent } from './components/credit-limit-early-repayment/credit-limit-early-repayment.component';
import { CreditLimitMoneyAllocationComponent } from './components/credit-limit-money-allocation/credit-limit-money-allocation.component';
import { CreditLimitNewInsuranceDialogComponent } from './components/credit-limit-new-insurance-dialog/credit-limit-new-insurance-dialog.component';
import { CreditLimitOwnerLifeInsuranceComponent } from './components/credit-limit-owner-life-insurance/credit-limit-owner-life-insurance.component';
import { CreditLimitOwnerComponent } from './components/credit-limit-owner/credit-limit-owner.component';
import { CreditLimitPortfolioChangeAmountComponent } from './components/credit-limit-portfolio-change-amount/credit-limit-portfolio-change-amount.component';
import { CreditLimitPortfolioChangeDateComponent } from './components/credit-limit-portfolio-change-date/credit-limit-portfolio-change-date.component';
import { CreditLimitPortfolioChangeEffectiveDateComponent } from './components/credit-limit-portfolio-change-effective-date/credit-limit-portfolio-change-effective-date.component';
import { CreditLimitPortfolioDisbursementComponent } from './components/credit-limit-portfolio-disbursement/credit-limit-portfolio-disbursement.component';
import { CreditLimitPortfolioEffectiveDateComponent } from './components/credit-limit-portfolio-effective-date/credit-limit-portfolio-effective-date.component';
import { CreditLimitPortfoliosComponent } from './components/credit-limit-portfolios/credit-limit-portfolios.component';
import { CreditLimitSimulateComponent } from './components/credit-limit-simulate/credit-limit-simulate.component';
import { CreditLimitStatusComponent } from './components/credit-limit-status/credit-limit-status.component';
import { EarlyRepaymentLayoutComponent } from './components/early-repayment-layout/early-repayment-layout.component';
import { EarlyRepaymentMoneyAllocationComponent } from './components/early-repayment-money-allocation/early-repayment-money-allocation.component';
import { LetterOfIntentComponent } from './components/letter-of-intent/letter-of-intent.component';

@NgModule({
  declarations: [
    CreditLimitDetailsLayoutComponent,
    CreditLimitPreviewComponent,
    CreditLimitActivityLogComponent,
    CreditLimitPortfoliosComponent,
    CreditLimitDetailsComponent,
    CreditLimitOwnerComponent,
    CreditLimitConsumersComponent,
    CreditLimitStatusComponent,
    CreditLimitChangeAmountComponent,
    CreditLimitPortfolioDisbursementComponent,
    CreditLimitPortfolioEffectiveDateComponent,
    CreditLimitPortfolioChangeDateComponent,
    CreditLimitPortfolioChangeAmountComponent,
    CreditLimitPortfolioChangeEffectiveDateComponent,
    CreditLimitCollateralsComponent,
    CreditLimitCollateralsOwnerDialogComponent,
    CreditLimitNewInsuranceDialogComponent,
    CreditLimitOwnerLifeInsuranceComponent,
    CreditLimitEarlyRepaymentComponent,
    CreditLimitMoneyAllocationComponent,
    EarlyRepaymentMoneyAllocationComponent,
    CreditLimitSimulateComponent,
    EarlyRepaymentLayoutComponent,
    LetterOfIntentComponent,
  ],
  imports: [
    CommonModule,
    CreditLimitDetailsRoutingModule,
    RouterModule,
    AngularMaterialModule,
    TranslocoModule,
    PrintAndDownloadModule,
    FiltersPanelModule,
    E2eLayoutModule,
    SearchFieldModule,
    FooterModule,
    FormsModule,
    ReactiveFormsModule,
    BackToPreviousPageModule,
    UploadDocumentsModule,
    NgxDocumentViewerModule,
    PrintAndDownloadModule,
    E2eBorrowerDetailsModule,
    E2eBorrowerViewModule,
    DoughnutChartModule,
    E2eOverdueTooltipModule,
    E2eActionsMenuModule,
    NgxMaskPipe,
    E2ePipesModule,
    TooltipModule,
    E2eLoanUploadDocumentsModule,
    NgxMaskDirective,
    E2eProductViewModule,
    LoaderModule,
    E2eLoanDisbursementFormModule,
    E2eLoanEffectiveDateFormModule,
    E2eLoanChangeDateFormModule,
    E2eLoanChangeAmountFormModule,
    E2eIdentityPartiesExistingApplicationModule,
    E2ePortfoliosDetailsLayoutModule,
    E2ePortfoliosSortModule,
    E2eAttachedDocumentsModule,
    E2eLifeInsuranceModule,
    E2eEarlyRepaymentPortfoliosModule,
    E2eEarlyRepaymentLoansModule,
    E2eEarlyRepaymentTracksModule,
    E2eDebtSimulateModule,
    StoreModule.forFeature(
      TmStateSlices.CreditLimitDetails,
      creditLimitDetailsReducer,
      {
        initialState,
      }
    ),
    StoreModule.forFeature(TmStateSlices.E2eLoan, e2eLoanStateReducers, {
      initialState: e2eLoanInitialState,
    }),
    EffectsModule.forFeature([
      CreditLimitDetailsEffects,
      ...e2eLoanStateEffects,
    ]),
    E2ePartiesViewModule,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: [
        {
          scope: 'e2e/credit-limit-details',
          alias: 'credit-limit-details',
        },
        {
          scope: 'e2e',
          alias: 'e2e',
        },
      ],
    },
  ],
})
export class CreditLimitDetailsModule {}
