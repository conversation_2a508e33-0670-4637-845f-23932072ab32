{"name": "e2e-lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/e2e-lib/src", "prefix": "e2e", "tags": [], "projectType": "library", "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/e2e-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/e2e-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/e2e-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/e2e-lib/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/e2e-lib/**/*.ts", "libs/e2e-lib/**/*.html", "libs/e2e-lib/filters-panel/**/*.ts", "libs/e2e-lib/filters-panel/**/*.html", "libs/e2e-lib/search-field/**/*.ts", "libs/e2e-lib/search-field/**/*.html", "libs/e2e-lib/ngx-document-viewer/**/*.ts", "libs/e2e-lib/ngx-document-viewer/**/*.html", "libs/e2e-lib/confirmation-dialog/**/*.ts", "libs/e2e-lib/confirmation-dialog/**/*.html", "libs/e2e-lib/services/**/*.ts", "libs/e2e-lib/services/**/*.html", "libs/e2e-lib/types/**/*.ts", "libs/e2e-lib/types/**/*.html", "libs/e2e-lib/e2e-layout/**/*.ts", "libs/e2e-lib/e2e-layout/**/*.html", "libs/e2e-lib/doughnut-chart/**/*.ts", "libs/e2e-lib/doughnut-chart/**/*.html", "libs/e2e-lib/services/**/*.ts", "libs/e2e-lib/services/**/*.html", "libs/e2e-lib/e2e-borrower-details/**/*.ts", "libs/e2e-lib/e2e-borrower-details/**/*.html", "libs/e2e-lib/state/**/*.ts", "libs/e2e-lib/state/**/*.html", "libs/e2e-lib/e2e-portfolio-loans/**/*.ts", "libs/e2e-lib/e2e-portfolio-loans/**/*.html", "libs/e2e-lib/e2e-identity-parties-form/**/*.ts", "libs/e2e-lib/e2e-identity-parties-form/**/*.html", "libs/e2e-lib/e2e-loan-upload-documents/**/*.ts", "libs/e2e-lib/e2e-loan-upload-documents/**/*.html", "libs/e2e-lib/e2e-portfolio-form/**/*.ts", "libs/e2e-lib/e2e-portfolio-form/**/*.html", "libs/e2e-lib/e2e-product-form/**/*.ts", "libs/e2e-lib/e2e-product-form/**/*.html", "libs/e2e-lib/e2e-limit-details-form/**/*.ts", "libs/e2e-lib/e2e-limit-details-form/**/*.html", "libs/e2e-lib/e2e-borrower-view/**/*.ts", "libs/e2e-lib/e2e-borrower-view/**/*.html", "libs/e2e-lib/e2e-parties-view/**/*.ts", "libs/e2e-lib/e2e-parties-view/**/*.html", "libs/e2e-lib/e2e-portfolio-view/**/*.ts", "libs/e2e-lib/e2e-portfolio-view/**/*.html", "libs/e2e-lib/e2e-product-view/**/*.ts", "libs/e2e-lib/e2e-product-view/**/*.html", "libs/e2e-lib/e2e-limit-details-view/**/*.ts", "libs/e2e-lib/e2e-limit-details-view/**/*.html", "libs/e2e-lib/e2e-portfolio-header/**/*.ts", "libs/e2e-lib/e2e-portfolio-header/**/*.html", "libs/e2e-lib/e2e-pipes/**/*.ts", "libs/e2e-lib/e2e-pipes/**/*.html", "libs/e2e-lib/e2e-portfolio-lien-form/**/*.ts", "libs/e2e-lib/e2e-portfolio-lien-form/**/*.html", "libs/e2e-lib/e2e-portfolio-collateral-form/**/*.ts", "libs/e2e-lib/e2e-portfolio-collateral-form/**/*.html", "libs/e2e-lib/e2e-portfolio-collaterals/**/*.ts", "libs/e2e-lib/e2e-portfolio-collaterals/**/*.html", "libs/e2e-lib/e2e-product-info/**/*.ts", "libs/e2e-lib/e2e-product-info/**/*.html", "libs/e2e-lib/e2e-identity-parties-existing-application/**/*.ts", "libs/e2e-lib/e2e-identity-parties-existing-application/**/*.html", "libs/e2e-lib/e2e-product-fee-info/**/*.ts", "libs/e2e-lib/e2e-product-fee-info/**/*.html", "libs/e2e-lib/e2e-loan-management-services/**/*.ts", "libs/e2e-lib/e2e-loan-management-services/**/*.html", "libs/e2e-lib/e2e-borrower-create/**/*.ts", "libs/e2e-lib/e2e-borrower-create/**/*.html", "libs/e2e-lib/e2e-actions-menu/**/*.ts", "libs/e2e-lib/e2e-actions-menu/**/*.html", "libs/e2e-lib/e2e-disabled-control/**/*.ts", "libs/e2e-lib/e2e-disabled-control/**/*.html", "libs/e2e-lib/e2e-repayments-simulation/**/*.ts", "libs/e2e-lib/e2e-repayments-simulation/**/*.html", "libs/e2e-lib/e2e-fee/**/*.ts", "libs/e2e-lib/e2e-fee/**/*.html", "libs/e2e-lib/e2e-loan-disbursement-form/**/*.ts", "libs/e2e-lib/e2e-loan-disbursement-form/**/*.html", "libs/e2e-lib/e2e-loan-header/**/*.ts", "libs/e2e-lib/e2e-loan-header/**/*.html", "libs/e2e-lib/e2e-loan-change-date-form/**/*.ts", "libs/e2e-lib/e2e-loan-change-date-form/**/*.html", "libs/e2e-lib/e2e-loan-change-amount-form/**/*.ts", "libs/e2e-lib/e2e-loan-change-amount-form/**/*.html", "libs/e2e-lib/e2e-application-details-layout/**/*.ts", "libs/e2e-lib/e2e-application-details-layout/**/*.html", "libs/e2e-lib/e2e-attached-documents/**/*.ts", "libs/e2e-lib/e2e-attached-documents/**/*.html", "libs/e2e-lib/credit-limit-allocation/**/*.ts", "libs/e2e-lib/credit-limit-allocation/**/*.html", "libs/e2e-lib/e2e-loan-effective-date-form/**/*.ts", "libs/e2e-lib/e2e-loan-effective-date-form/**/*.html", "libs/e2e-lib/e2e-set-effective-date-form/**/*.ts", "libs/e2e-lib/e2e-set-effective-date-form/**/*.html", "libs/e2e-lib/e2e-loan-effective-date-view/**/*.ts", "libs/e2e-lib/e2e-loan-effective-date-view/**/*.html", "libs/e2e-lib/e2e-portfolios-details-layout/**/*.ts", "libs/e2e-lib/e2e-portfolios-details-layout/**/*.html", "libs/e2e-lib/e2e-portfolios-sort/**/*.ts", "libs/e2e-lib/e2e-portfolios-sort/**/*.html", "libs/e2e-lib/e2e-portfolio-product/**/*.ts", "libs/e2e-lib/e2e-portfolio-product/**/*.html", "libs/e2e-lib/e2e-early-repayment-view/**/*.ts", "libs/e2e-lib/e2e-early-repayment-view/**/*.html", "libs/e2e-lib/e2e-early-repayment-form/**/*.ts", "libs/e2e-lib/e2e-early-repayment-form/**/*.html", "libs/e2e-lib/e2e-freeze-payment-form/**/*.ts", "libs/e2e-lib/e2e-freeze-payment-form/**/*.html", "libs/e2e-lib/e2e-change-payment-date-form/**/*.ts", "libs/e2e-lib/e2e-change-payment-date-form/**/*.html", "libs/e2e-lib/e2e-unfreeze-payment-form/**/*.ts", "libs/e2e-lib/e2e-unfreeze-payment-form/**/*.html", "libs/e2e-lib/e2e-loan-details-preview/**/*.ts", "libs/e2e-lib/e2e-loan-details-preview/**/*.html", "libs/e2e-lib/e2e-simulate-repayment-preview/**/*.ts", "libs/e2e-lib/e2e-simulate-repayment-preview/**/*.html", "libs/e2e-lib/simulated-credit-limit-view/**/*.ts", "libs/e2e-lib/simulated-credit-limit-view/**/*.html", "libs/e2e-lib/simulated-portfolio-view/**/*.ts", "libs/e2e-lib/simulated-portfolio-view/**/*.html", "libs/e2e-lib/stacked-bar-chart/**/*.ts", "libs/e2e-lib/stacked-bar-chart/**/*.html", "libs/e2e-lib/e2e-portfolio-tracks/**/*.ts", "libs/e2e-lib/e2e-portfolio-tracks/**/*.html", "libs/e2e-lib/e2e-identity-parties/**/*.ts", "libs/e2e-lib/e2e-identity-parties/**/*.html", "libs/e2e-lib/e2e-life-insurance/**/*.ts", "libs/e2e-lib/e2e-life-insurance/**/*.html", "libs/e2e-lib/e2e-portfolio-payers/**/*.ts", "libs/e2e-lib/e2e-portfolio-payers/**/*.html", "libs/e2e-lib/e2e-regulatory-reports/**/*.ts", "libs/e2e-lib/e2e-regulatory-reports/**/*.html", "libs/e2e-lib/e2e-loans-overdue-payment/**/*.ts", "libs/e2e-lib/e2e-loans-overdue-payment/**/*.html", "libs/e2e-lib/e2e-early-repayment-portfolios/**/*.ts", "libs/e2e-lib/e2e-early-repayment-portfolios/**/*.html", "libs/e2e-lib/e2e-early-repayment-tracks/**/*.ts", "libs/e2e-lib/e2e-early-repayment-tracks/**/*.html", "libs/e2e-lib/e2e-early-repayment-loans/**/*.ts", "libs/e2e-lib/e2e-early-repayment-loans/**/*.html", "libs/e2e-lib/e2e-overdue-tooltip/**/*.ts", "libs/e2e-lib/e2e-overdue-tooltip/**/*.html", "libs/e2e-lib/e2e-debt-simulate/**/*.ts", "libs/e2e-lib/e2e-debt-simulate/**/*.html"]}}}}