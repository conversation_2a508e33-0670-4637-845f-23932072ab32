import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { E2eService } from '@e2e/lib/services';
import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { catchError, filter, map, mergeMap, tap } from 'rxjs/operators';
import { ApplicationCreditLimitEalyRepaymentService } from '../services/application-credit-limit-early-repayment.service';

import { MatDialog } from '@angular/material/dialog';
import { DebtSimulationComponent } from '@e2e/lib/e2e-debt-simulate';
import { RepaymentsSimulationComponent } from '@e2e/lib/e2e-repayments-simulation';
import {
  ApplicationTypes,
  getBorrowerName,
  LoanRepaymentDetails,
} from '@e2e/lib/types';
import { LanguageService } from '@tenant-management/lib/services';
import { selectApplicationId } from '.';
import * as ApplicationCreditLimitEarlyRepaymentApiActions from './actions/application-credit-limit-early-repayment-api.actions';
import * as ApplicationCreditLimitEarlyRepaymentPageActions from './actions/application-credit-limit-early-repayment-page.actions';
import { getCreditLimitEarlyRepaymentApplication } from './actions/application-credit-limit-early-repayment-page.actions';

@Injectable()
export class ApplicationCreditLimitEarlyRepaymentEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private e2eService: E2eService,
    private router: Router,
    private creditLimitearlyRepaymentService: ApplicationCreditLimitEalyRepaymentService,
    private dialog: MatDialog,
    private languageService: LanguageService
  ) {}

  getCreditLimitEarlyRepaymentApplication$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getCreditLimitEarlyRepaymentApplication),
      concatLatestFrom(() => this.store.select(selectApplicationId)),
      filter(
        ([{ type, applicationType }, applicationId]) =>
          applicationType === ApplicationTypes.CreditLimitEarlyRepayment &&
          !!applicationId
      ),
      mergeMap(([{ applicationType }, applicationId]) =>
        this.e2eService
          .getCreaditLimitEarlyRepaymnetApplication(applicationId)
          .pipe(
            filter((response) => !response.loading),
            map((response) => {
              if (response.data?.creditLimit.id) {
                this.store.dispatch(
                  ApplicationCreditLimitEarlyRepaymentPageActions.getCreditLimitEarlyRepaymnetOverdue(
                    { creditLimitId: response.data?.creditLimit.id }
                  )
                );
              }
              return ApplicationCreditLimitEarlyRepaymentPageActions.getIdentityParties(
                { application: response.data }
              );
            }),
            catchError((error: unknown) => {
              return of(
                ApplicationCreditLimitEarlyRepaymentApiActions.getLimitEarlyRepaymentApplicationFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  getIdentityParties$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.getIdentityParties
      ),
      mergeMap(({ application }) => {
        const ids = application.payers
          ? `${application.requestedBy.identityPartyId},${application.payers[0].identityPartyId}`
          : application.requestedBy.identityPartyId;
        return this.e2eService.getIdentityParties(ids).pipe(
          map((identityParties) => {
            return ApplicationCreditLimitEarlyRepaymentApiActions.getLimitEarlyRepaymentApplicationSuccess(
              {
                application,
                identityParties,
              }
            );
          }),
          catchError((error: unknown) => {
            return of(
              ApplicationCreditLimitEarlyRepaymentApiActions.getLimitEarlyRepaymentApplicationFailure(
                { error }
              )
            );
          })
        );
      })
    );
  });

  sendApplicationDecision$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.sendApplicationDecision
      ),
      concatLatestFrom(() => this.store.select(selectApplicationId)),
      mergeMap(([{ type, applicationDecision }, id]) =>
        this.creditLimitearlyRepaymentService
          .sendApplicationDecision(id, applicationDecision)
          .pipe(
            map(() => {
              return ApplicationCreditLimitEarlyRepaymentApiActions.sendApplicationDecisionSuccess();
            }),
            catchError((error: unknown) => {
              return of(
                ApplicationCreditLimitEarlyRepaymentApiActions.sendApplicationDecisionFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  getCreditLimitOverdue$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.getCreditLimitEarlyRepaymnetOverdue
      ),
      mergeMap(({ type, creditLimitId }) =>
        this.e2eService.getCreditLimitoverdue(creditLimitId).pipe(
          map((creditLimitOverdue) => {
            return ApplicationCreditLimitEarlyRepaymentApiActions.getCreditLimitearlyRepaymentDetailsSuccess(
              { creditLimitOverdue }
            );
          }),
          catchError((error: unknown) => {
            return of(
              ApplicationCreditLimitEarlyRepaymentApiActions.getCreditLimitearlyRepaymentDetailsFailure(
                {
                  error,
                }
              )
            );
          })
        )
      )
    );
  });

  navigateToApplications$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          ApplicationCreditLimitEarlyRepaymentApiActions.sendApplicationDecisionSuccess
        ),
        tap(() => {
          this.router.navigateByUrl('operations/applications');
        })
      );
    },
    { dispatch: false }
  );

  getLoanFullRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.getLoanFullRepayments
      ),
      mergeMap(({ loanId, effectiveDate, fees }) =>
        this.creditLimitearlyRepaymentService
          .getFullRepaymentSimulate(loanId, effectiveDate, fees)
          .pipe(
            map((loanSimulatedRepayment) => {
              return ApplicationCreditLimitEarlyRepaymentApiActions.getLoanPartialRepaymentsSimulationSuccess(
                {
                  loanSimulatedRepayment,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                ApplicationCreditLimitEarlyRepaymentApiActions.getLoanPartialRepaymentsSimulationFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  getLoanPartialRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.getLoanPartialRepayments
      ),
      mergeMap(
        ({ loanId, effectiveDate, fees, earlyRepaymentAmount, spreadMethod }) =>
          this.creditLimitearlyRepaymentService
            .getPartialRepaymentSimulate(
              loanId,
              effectiveDate,
              fees,
              earlyRepaymentAmount,
              spreadMethod
            )
            .pipe(
              map((loanSimulatedRepayment) => {
                return ApplicationCreditLimitEarlyRepaymentApiActions.getLoanPartialRepaymentsSimulationSuccess(
                  {
                    loanSimulatedRepayment,
                  }
                );
              }),
              catchError((error: unknown) => {
                return of(
                  ApplicationCreditLimitEarlyRepaymentApiActions.getLoanPartialRepaymentsSimulationFailure(
                    {
                      error,
                    }
                  )
                );
              })
            )
      )
    );
  });

  getLoanDebtRepayments$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        ApplicationCreditLimitEarlyRepaymentPageActions.getLoanDebtRepayments
      ),
      concatLatestFrom(() => this.store.select(selectApplicationId)),
      mergeMap(([{ limitId }, applicationId]) =>
        // mergeMap(({ limitId, effectiveDate, amount }) =>
        this.creditLimitearlyRepaymentService
          // .getDebtRepaymentSimulate(limitId, effectiveDate, amount)
          .getDebtRepaymentSimulate(applicationId)
          .pipe(
            map((loanSimulatedRepayment) => {
              return ApplicationCreditLimitEarlyRepaymentApiActions.getLoanDebtRepaymentsSuccess(
                {
                  loanSimulatedRepayment,
                }
              );
            }),
            catchError((error: unknown) => {
              return of(
                ApplicationCreditLimitEarlyRepaymentApiActions.getLoanDebtRepaymentsFailure(
                  {
                    error,
                  }
                )
              );
            })
          )
      )
    );
  });

  openRepaymentsSimulation$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          ApplicationCreditLimitEarlyRepaymentApiActions.getLoanPartialRepaymentsSimulationSuccess
        ),
        tap(({ type, loanSimulatedRepayment }) => {
          const loanRepaymentDetails: LoanRepaymentDetails = {
            loanDetails: loanSimulatedRepayment,
            loanPayments: [
              ...(loanSimulatedRepayment.disbursements || []),
              ...(loanSimulatedRepayment.payments || []),
            ],
            borrowerName: getBorrowerName(
              loanSimulatedRepayment.loanIdentityParties
            ),
            title: 'e2e.repaymentTable.repaymentsSimulationTable',
          };

          this.dialog.open(RepaymentsSimulationComponent, {
            disableClose: true,
            minWidth: '1050px',
            maxWidth: '95vw',
            data: loanRepaymentDetails,
            direction: this.languageService.getDirection(),
          });
        })
      );
    },
    { dispatch: false }
  );

  openLoanDebtRepaymentSimulation$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          ApplicationCreditLimitEarlyRepaymentApiActions.getLoanDebtRepaymentsSuccess
        ),
        tap(({ type, loanSimulatedRepayment }) => {
          this.dialog.open(DebtSimulationComponent, {
            disableClose: true,
            minWidth: '1050px',
            maxWidth: '95vw',
            data: loanSimulatedRepayment,
            direction: this.languageService.getDirection(),
          });
        })
      );
    },
    { dispatch: false }
  );
}
