<section>
  <header class="flex items-baseline mb-6">
    <h3 class="text-[22px] font-bold text-compass">
      {{ 'credit-limit-details.letterOfIntent.title' | transloco }}
    </h3>
    <tm-tooltip [escape]="false" [tooltipTemplateMessage]="tooltipTemplate">
      <div #tooltipTemplate>
        <div class="w-[300px]">
          The Letter of intent will be visible in the document section under the
          Credit limit
        </div>
      </div>
    </tm-tooltip>
  </header>

  <form [formGroup]="letterOfIntentForm">
    <mat-form-field class="w-full">
      <mat-label>
        {{ 'credit-limit-details.letterOfIntent.organizationName' | transloco }}
      </mat-label>
      <input matInput formControlName="organizationName" />
    </mat-form-field>

    <mat-form-field class="w-full">
      <mat-label>
        {{ 'credit-limit-details.letterOfIntent.valueDate' | transloco }}
      </mat-label>
      <input matInput [matDatepicker]="valueDate" [matDatepickerFilter]="filterAvailableDates" formControlName="valueDate" />
      <mat-datepicker-toggle
        matSuffix
        [for]="valueDate"
      ></mat-datepicker-toggle>
      <mat-datepicker #valueDate></mat-datepicker>
    </mat-form-field>

    <footer class="flex justify-between mt-6">
      <button
        mat-stroked-button
        color="primary"
        class="!min-w-[190px] !h-10 !text-orientals !rounded-lg !border-orientals"
        routerLink="../"
      >
        {{ 'general.buttons.cancel' | transloco | uppercase }}
      </button>
      <button
        mat-flat-button
        color="primary"
        class="!min-w-[190px] !h-10 !rounded-lg"
        routerLink="../"
        (click)="generateLetterOfIntent()"
      >
        {{ 'general.buttons.generate' | transloco | uppercase }}
      </button>
    </footer>
  </form>
</section>
