{"title": "Credit limit", "matTabLinks": {"details": "Details", "limitDetails": "Limit details", "portfolios": "Portfolios", "borrower": "<PERSON><PERSON><PERSON>", "owner": "Owner", "limitOwner": "Limit owner", "consumers": "Beneficiaries", "limitConsumers": "Limit beneficiaries", "changeRequest": "Change request", "changeCreditLimitAmount": "Change Credit limit amount", "collaterals": "Collaterals", "request": "Request"}, "activityLog": {"title": "Activity trail"}, "businessDetails": {"title": "Business details"}, "details": {"title": "Limit details", "MONTHS": "Months", "DAYS": "Days"}, "creditLimitDetailsTableHeaders": {"borrower": "<PERSON><PERSON><PERSON>", "portfolioId": "Portfolio ID", "totalAmount": "Total amount", "purpose": "Purpose"}, "creditLimitStatus": {"title": "Limit status", "portfoliosCreated": "Portfolios created", "availableAmount": "Available amount", "utilized": "Utilized", "limitAmount": "Limit amount", "totalOverdue": "Total overdue"}, "changeAmount": {"title": "Change Limit amount", "currentLimitAmount": "Current limit amount", "newLimitAmount": "New limit amount", "tooltipMessage": "You could change here the Credit limit amount\n and create an application for approval.\nThe New Limit amount must be higher or\n equal to the Portfolios created. ", "changeAmountError": "The New Credit limit amount must be higher or equal to the sum of all Portfolios!"}, "portfolios": {"title": "Portfolios"}, "collaterals": {"title": "Collaterals", "allocatedMv": "Allocated MV", "lienAmount": "Lien amount", "mvCoverage": "MV Coverage", "lienDetails": {"title": "Lien details", "lienAmount": "Lien amount", "allocatedMarketValue": "Allocated market value", "referenceName": "Reference name", "registrationType": "Registration type", "registrationDate": "Registration date", "expiryDate": "Expiry date", "lienRank": "Lien rank", "pariPassu": {"title": "<PERSON><PERSON>", "0": "NO", "1": "YES"}, "description": "Description", "lienDocuments": "Lien documents"}, "collateralDefinition": {"assessmentDate": "Assessment date", "valueUpdateDate": "Value update date", "propertyUsage": "Property usage", "country": "Country", "region": "Region", "state": "State", "block": "Block", "plot": "Plot", "subplot": "Subplot", "postalCode": "Postal code", "city": "City", "street": "Street, number and name", "apartment": "Apartment", "addressDetails": "Address details", "noInsurance": "The collateral does not have active asset insurance"}, "newInsurance": {"saveBtn": "Save and close"}}, "noInsuranceCreditLimit": "The <span class=\"lowercase\">{{param}}</span> does not have life insurance for this credit limit.", "beneficiary": {"title": "Beneficiary", "multipleBeneficiary": "Beneficiaries", "name": "Beneficiary name", "uniqueId": "Unique ID", "type": "Beneficiary type", "distributionPercentage": "Distribution percentage", "totalPayoutAmount": "Total payout amount", "totalUnutilizedAmount": "Total unutilized amount", "payoutAmount": "Payout amount", "unutilizedAmount": "Unutilized amount", "paymentMethod": {"title": "Payment method", "paymentGateway": "Payment gateway", "accountNumber": "Account number", "payout": "Payout"}}, "earlyRepayment": {"layoutTitle": "Credit limit early repayment", "title": "Repayment request", "creditLimitBalance": "Credit limit balance", "totalOverdueAmount": "Total overdue amount", "typeOfEarlyRepayment": "Type of early repayment", "full": "Full", "partial": "Partial", "relatedParty": "Related party", "requestDate": "Request date", "effectiveDate": "Effective date", "amountToRepay": "Amount to repay", "repaymentDistribution": "Repayment distribution", "keepRepaymentAmount": "Keep repayment amount", "keepMaturityDate": "Keep maturity date", "moneyAllocation": "Money allocation", "automatic": "Automatic", "manual": "Manual", "fees": "Fees", "operationalFee": "Operational fee", "earlyNoticeFee": "Early notice fee", "capitalizationFee": "Capitalization fee", "charge": "Charge", "payer": "Payer", "amountErrorMessage": "Amount is higher than total overdue amount", "overdue": "Overdue", "overdueRepayment": "Overdue repayment", "portfolio": "Portfolio"}, "moneyAllocation": {"moneyAllocationTitle": "Money Allocation", "moneyAllocationWarningText": "The overdue amount is equal or higher than amount you want to repay. Only overdue repayment will be performed!", "portfolios": "Portfolios", "tracks": "Tracks", "loans": "Loans", "fullRepayment": "FULL REPAYMENT", "selected": "Selected", "outOf": "OUT OF", "track": "Track", "product": "Product", "loan": "Loan", "amountYouWantToRepay": "Amount you want to repay", "totalOverdueAmount": "Total overdue amount", "amountForEarlyRepayment": "Amount for early repayment", "remaining": "Remaining", "totalSelected": "Total selected", "partialAmountRepayment": "Partial amount repayment", "amountErrorMessage": "Amount is higher than full repayment amount"}, "portfolioTooltip": {"overdueAmount": "Overdue Amount", "portfolioAmount": "Portfolio amount", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount", "purpose": "Purpose", "effectiveDate": "Effective date", "endUtilizationDate": "End utilization date", "maturityDate": "Maturity date"}, "trackTooltip": {"trackAmount": "Track Amount", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount"}, "loanTooltip": {"loanAmount": "<PERSON><PERSON>", "outstandingPrincipal": "Outstanding principal", "amountFullRepayment": "Amount full repayment", "disbursementDate": "Disbursement date", "maturityDate": "Maturity date", "status": "Status", "numberOfPayments": "Number of payments", "interestType": "Interest Type", "regularInterestRate": "Regular interest rate", "financialSource": "Financial source"}, "borrowerRoles": {"BORROWER": "<PERSON><PERSON><PERSON>", "CO_BORROWER": "Co-borrower"}, "letterOfIntent": {"title": "Letter of intent", "valueDate": "Value date", "organizationName": "Organization name"}}