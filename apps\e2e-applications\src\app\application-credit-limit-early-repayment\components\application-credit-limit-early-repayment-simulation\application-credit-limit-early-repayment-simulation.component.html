<ng-container *ngIf="portfolios$ | async as simulationDetails">
  <ng-container *ngIf="getDateFormat$ | async as dateFormat">
    <ng-container *ngIf="getCurrency$ | async as currency">
      <div class="mb-6 main-card" *ngIf="simulationDetails.totalDebtAmount > 0">
        <div class="border-compass">
          <div class="grid grid-cols-12 items-center gap-4 mb-2">
            <div class="col-span-8 flex text-compass-87 font-semibold text-2xl">
              <div>
                {{
                  'application-details.credit-limit-early-repayment.overdue'
                    | transloco
                }}
              </div>
              <ng-container
                *ngIf="simulationDetails.creditLimit?.overdue as overdue"
              >
                <e2e-overdue-tooltip
                  class="leading-[0] self-center"
                  [totalOverdue]="overdue"
                  tooltipPosition="left"
                ></e2e-overdue-tooltip>
              </ng-container>
            </div>
            <div class="col-span-2 text-right me-4">
              <div
                *ngIf="
                  simulationDetails.repaymentType === repaymentTypes.Full ||
                    (simulationDetails.repaymentType ===
                      repaymentTypes.Partial &&
                      simulationDetails.amountToRepay &&
                      simulationDetails.amountToRepay >=
                        simulationDetails.totalDebtAmount);
                  else partialMode
                "
                class="operation-status early-repayment-status-full"
              >
                {{
                  'application-details.credit-limit-early-repayment.full'
                    | transloco
                    | uppercase
                }}
              </div>

              <ng-template #partialMode>
                <div class="operation-status early-repayment-status-partial">
                  {{
                    'application-details.credit-limit-early-repayment.partial'
                      | transloco
                      | uppercase
                  }}
                </div>
              </ng-template>
            </div>
            <div
              class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
            >
              <div
                *ngIf="
                  simulationDetails.amountToRepay &&
                    simulationDetails.amountToRepay > 0 &&
                    simulationDetails.amountToRepay <
                      simulationDetails.totalDebtAmount;
                  else partialModeTotalDebtAmount
                "
              >
                {{
                  simulationDetails.amountToRepay | currency: currency[0].code
                }}
              </div>
              <ng-template #partialModeTotalDebtAmount>
                {{
                  simulationDetails.totalDebtAmount | currency: currency[0].code
                }}
              </ng-template>
            </div>
          </div>

          <div class="bg-maWhite rounded-lg px-6 py-4">
            <div class="grid grid-cols-12 items-center gap-4">
              <div class="col-span-3 text-compass font-semibold text-lg">
                {{
                  'application-details.credit-limit-early-repayment.overdueRepayment'
                    | transloco
                }}
              </div>
              <div class="col-span-5 text-compass-87 font-semibold text-xs">
                <ng-container
                  *ngIf="getApplicationStatus$ | async as getApplicationStatus"
                >
                  <!-- <button
                    *ngIf="
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openDebtRepaymentTable(
                        simulationDetails.totalDebtAmount,
                        simulationDetails.effectiveDate,
                        simulationDetails.creditLimit?.id
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button> -->

                  <div
                    *ngIf="
                      simulationDetails.amountToRepay &&
                        simulationDetails.amountToRepay > 0 &&
                        simulationDetails.amountToRepay <
                          simulationDetails.totalDebtAmount;
                      else partialModeButtonSimulate
                    "
                  >
                    <button
                      *ngIf="
                        getApplicationStatus !== applicationStatuses.Completed
                      "
                      mat-button
                      type="button"
                      color="primary"
                      class="!rounded-lg !h-[35px]"
                      (click)="
                        openDebtRepaymentTable(
                          simulationDetails.amountToRepay,
                          simulationDetails.effectiveDate,
                          simulationDetails.creditLimit?.id
                        )
                      "
                    >
                      <div class="flex items-center">
                        <span class="me-2 pt-2">
                          <mat-icon svgIcon="icon-eye"></mat-icon>
                        </span>
                        <span class="text-sm font-medium">
                          {{
                            'general.buttons.viewSimulation'
                              | transloco
                              | titlecase
                          }}
                        </span>
                      </div>
                    </button>
                  </div>
                  <ng-template #partialModeButtonSimulate>
                    <button
                      *ngIf="
                        getApplicationStatus !== applicationStatuses.Completed
                      "
                      mat-button
                      type="button"
                      color="primary"
                      class="!rounded-lg !h-[35px]"
                      (click)="
                        openDebtRepaymentTable(
                          simulationDetails.totalDebtAmount,
                          simulationDetails.effectiveDate,
                          simulationDetails.creditLimit?.id
                        )
                      "
                    >
                      <div class="flex items-center">
                        <span class="me-2 pt-2">
                          <mat-icon svgIcon="icon-eye"></mat-icon>
                        </span>
                        <span class="text-sm font-medium">
                          {{
                            'general.buttons.viewSimulation'
                              | transloco
                              | titlecase
                          }}
                        </span>
                      </div>
                    </button>
                  </ng-template>
                </ng-container>
              </div>
              <div class="col-span-2 text-right">
                <div
                  *ngIf="
                    simulationDetails.repaymentType === repaymentTypes.Full ||
                      (simulationDetails.repaymentType ===
                        repaymentTypes.Partial &&
                        simulationDetails.amountToRepay &&
                        simulationDetails.amountToRepay >=
                          simulationDetails.totalDebtAmount);
                    else partialModeRepayment
                  "
                  class="operation-status early-repayment-status-full"
                >
                  {{
                    'application-details.credit-limit-early-repayment.full'
                      | transloco
                      | uppercase
                  }}
                </div>

                <ng-template #partialModeRepayment>
                  <div class="operation-status early-repayment-status-partial">
                    {{
                      'application-details.credit-limit-early-repayment.partial'
                        | transloco
                        | uppercase
                    }}
                  </div>
                </ng-template>
              </div>
              <div
                class="col-span-2 text-compass-87 font-semibold text-lg text-right"
              >
                <div
                  *ngIf="
                    simulationDetails.amountToRepay &&
                      simulationDetails.amountToRepay > 0 &&
                      simulationDetails.amountToRepay <
                        simulationDetails.totalDebtAmount;
                    else partialModeRepaymentTotalDebtAmount
                  "
                >
                  {{
                    simulationDetails.amountToRepay | currency: currency[0].code
                  }}
                </div>
                <ng-template #partialModeRepaymentTotalDebtAmount>
                  {{
                    simulationDetails.totalDebtAmount
                      | currency: currency[0].code
                  }}
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="
          simulationDetails.repaymentType === repaymentTypes.Full ||
          (simulationDetails.repaymentType === repaymentTypes.Partial &&
            simulationDetails.amountToRepay &&
            simulationDetails.amountToRepay > simulationDetails.totalDebtAmount)
        "
        class="mb-6 main-card"
      >
        <div
          *ngFor="let portfolio of simulationDetails.portfolios"
          class="border-compass mb-4"
        >
          <div class="grid grid-cols-12 items-center mb-2 gap-4">
            <div class="col-span-8 text-compass-87 font-semibold text-2xl">
              <span>
                {{ 'e2e.products.portfolio' | transloco }}
                {{ portfolio.referenceName }}
              </span>
              <tm-tooltip
                [escape]="false"
                [tooltipTemplateMessage]="portfolioTooltipTemplate"
              >
                <div #portfolioTooltipTemplate>
                  <div class="grid grid-cols-2 gap-2">
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.portfolioAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.portfolioAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.utilizedAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.utilizedAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.unutilizedAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.unutilizedAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.purpose'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.purpose | transloco }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.effectiveDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.effectiveDate | date: dateFormat }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.endUtilizationDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.endUtilizationDate | date: dateFormat }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.maturityDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.maturityDate | date: dateFormat }}
                    </div>
                  </div>
                </div>
              </tm-tooltip>
            </div>
            <div class="col-span-2 text-right me-4">
              <div
                class="operation-status"
                [ngClass]="
                  'early-repayment-status-' + portfolio.earlyRepaymentType
                    | lowercase
                "
              >
                {{
                  'application-details.credit-limit-early-repayment.' +
                    (portfolio.earlyRepaymentType | lowercase)
                    | transloco
                    | uppercase
                }}
              </div>
            </div>
            <div
              class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
            >
              {{ portfolio.earlyRepaymentAmount | currency: currency[0].code }}
            </div>
          </div>

          <div
            class="bg-maWhite rounded-lg px-6 py-4 mb-4"
            *ngFor="let track of portfolio.tracks; let i = index"
          >
            <div
              class="grid grid-cols-12 items-center gap-4 border-b mb-4 pb-4"
            >
              <div class="col-span-3 text-lead font-black text-lg">
                <span>
                  {{ 'e2e.portfolioAndCollateral.track' | transloco }}
                  #{{ track.referenceName }}
                </span>
                <tm-tooltip
                  [escape]="false"
                  [tooltipTemplateMessage]="trackTooltipTemplate"
                >
                  <div #trackTooltipTemplate>
                    <div class="grid grid-cols-2 gap-2">
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.trackAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.trackAmount | currency: currency[0].code }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.utilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.utilizedAmount | currency: currency[0].code }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.unutilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          track.unutilizedAmount | currency: currency[0].code
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.product'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.product }}
                      </div>
                    </div>
                  </div>
                </tm-tooltip>
              </div>
              <div class="col-span-5 text-compass-87 font-semibold text-xs">
                {{
                  'application-details.credit-limit-early-repayment.product'
                    | transloco
                }}
                <span class="text-lead">
                  {{ track.product }}
                </span>
              </div>

              <div class="col-span-2 text-right">
                <div
                  class="operation-status"
                  [ngClass]="
                    'early-repayment-status-' + track.earlyRepaymentType
                      | lowercase
                  "
                >
                  {{
                    'application-details.credit-limit-early-repayment.' +
                      (track.earlyRepaymentType | lowercase)
                      | transloco
                      | uppercase
                  }}
                </div>
              </div>
              <div
                class="col-span-2 text-lead font-semibold text-lg text-right"
              >
                {{ track.earlyRepaymentAmount | currency: currency[0].code }}
              </div>
            </div>

            <div
              *ngFor="let loan of track.loans; let loanIndex = index"
              class="grid grid-cols-12 mb-2 items-center gap-4"
            >
              <div
                class="col-span-3 text-compass font-semibold text-sm flex items-center"
              >
                <div>
                  <span>
                    {{ 'e2e.disbursement.loan' | transloco }}
                    #{{ loan.referenceName }}
                  </span>
                  <tm-tooltip
                    [escape]="false"
                    [tooltipTemplateMessage]="loanTooltipTemplate"
                  >
                    <div #loanTooltipTemplate>
                      <div class="grid grid-cols-2 gap-2">
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.loanAmount'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.loanAmount | currency: currency[0].code }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.outstandingPrincipal'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.outstandingPrincipal
                              | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.amountFullRepayment'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.amountFullRepayment
                              | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.disbursementDate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.disbursementDate | date: 'dd.MM.yyyy' }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.maturityDate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.maturityDate | date: 'dd.MM.yyyy' }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.status'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.status }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.numberOfPayments'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.numberOfPayments }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.interestType'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.interestType }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.regularInterestRate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.regularInterestRate * 100 | number: '1.2-2'
                          }}%
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.financialSource'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.financialSource }}
                        </div>
                      </div>
                    </div>
                  </tm-tooltip>
                </div>
              </div>
              <div class="col-span-5">
                <ng-container
                  *ngIf="getApplicationStatus$ | async as getApplicationStatus"
                >
                  <button
                    *ngIf="
                      loan.earlyRepaymentType === repaymentTypes.Full &&
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openFullRepaymentTable(
                        loan.id,
                        simulationDetails.effectiveDate,
                        simulationDetails.fees
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                  <button
                    *ngIf="
                      loan.earlyRepaymentType === repaymentTypes.Partial &&
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openPartialRepaymentTable(
                        loan.id,
                        simulationDetails.effectiveDate,
                        simulationDetails.fees,
                        loan.earlyRepaymentAmount,
                        simulationDetails.repaymentDistribution
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                </ng-container>
              </div>

              <div class="col-span-2 text-right">
                <div
                  class="operation-status"
                  [ngClass]="
                    'early-repayment-status-' + loan.earlyRepaymentType
                      | lowercase
                  "
                >
                  {{
                    'application-details.credit-limit-early-repayment.' +
                      (loan.earlyRepaymentType | lowercase)
                      | transloco
                      | uppercase
                  }}
                </div>
              </div>
              <div
                class="col-span-2 text-compass-87 font-semibold text-lg text-right"
              >
                <span
                  *ngIf="loan.earlyRepaymentType === repaymentTypes.Partial"
                >
                  {{ loan.earlyRepaymentAmount | currency: currency[0].code }}
                </span>
                <span *ngIf="loan.earlyRepaymentType === repaymentTypes.Full">
                  {{ loan.amountFullRepayment | currency: currency[0].code }}
                </span>
              </div>
            </div>
            <!--end.loans-->
          </div>
          <!--end.tracks-->
        </div>
        <!--end.portfolios-->
      </div>
    </ng-container>
  </ng-container>
</ng-container>
