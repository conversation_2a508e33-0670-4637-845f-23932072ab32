import { ChangeDetectionStrategy, Component } from '@angular/core';
import { NonNullableFormBuilder, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { CreditLimitDetailsPageActions } from '../../+state/actions';

@Component({
  selector: 'e2e-letter-of-intent',
  templateUrl: './letter-of-intent.component.html',
  styleUrls: ['./letter-of-intent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LetterOfIntentComponent {
  letterOfIntentForm = this.fb.group({
    organizationName: '',
    valueDate: ['', Validators.required],
  });

  constructor(private fb: NonNullableFormBuilder, private store: Store) {}

  filterAvailableDates(d: Date | null): boolean{
    
  }

  generateLetterOfIntent() {
    this.letterOfIntentForm.markAllAsTouched();

    if (this.letterOfIntentForm.valid) {
      const letterOfIntent = this.letterOfIntentForm.getRawValue();
      this.store.dispatch(
        CreditLimitDetailsPageActions.generateLetterOfIntent({
          letterOfIntent,
        })
      );
    }
  }
}
