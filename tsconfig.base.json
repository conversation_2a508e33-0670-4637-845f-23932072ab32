{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@assets": ["libs/assets/src/index.ts"], "@e2e/lib": ["libs/e2e-lib/src/index.ts"], "@e2e/lib/confirmation-dialog": ["libs/e2e-lib/confirmation-dialog/src/index.ts"], "@e2e/lib/credit-limit-allocation": ["libs/e2e-lib/credit-limit-allocation/src/index.ts"], "@e2e/lib/doughnut-chart": ["libs/e2e-lib/doughnut-chart/src/index.ts"], "@e2e/lib/e2e-actions-menu": ["libs/e2e-lib/e2e-actions-menu/src/index.ts"], "@e2e/lib/e2e-application-details-layout": ["libs/e2e-lib/e2e-application-details-layout/src/index.ts"], "@e2e/lib/e2e-attached-documents": ["libs/e2e-lib/e2e-attached-documents/src/index.ts"], "@e2e/lib/e2e-borrower-create": ["libs/e2e-lib/e2e-borrower-create/src/index.ts"], "@e2e/lib/e2e-borrower-details": ["libs/e2e-lib/e2e-borrower-details/src/index.ts"], "@e2e/lib/e2e-borrower-view": ["libs/e2e-lib/e2e-borrower-view/src/index.ts"], "@e2e/lib/e2e-change-payment-date-form": ["libs/e2e-lib/e2e-change-payment-date-form/src/index.ts"], "@e2e/lib/e2e-debt-simulate": ["libs/e2e-lib/e2e-debt-simulate/src/index.ts"], "@e2e/lib/e2e-disabled-control": ["libs/e2e-lib/e2e-disabled-control/src/index.ts"], "@e2e/lib/e2e-early-repayment-form": ["libs/e2e-lib/e2e-early-repayment-form/src/index.ts"], "@e2e/lib/e2e-early-repayment-loans": ["libs/e2e-lib/e2e-early-repayment-loans/src/index.ts"], "@e2e/lib/e2e-early-repayment-portfolios": ["libs/e2e-lib/e2e-early-repayment-portfolios/src/index.ts"], "@e2e/lib/e2e-early-repayment-tracks": ["libs/e2e-lib/e2e-early-repayment-tracks/src/index.ts"], "@e2e/lib/e2e-early-repayment-view": ["libs/e2e-lib/e2e-early-repayment-view/src/index.ts"], "@e2e/lib/e2e-fee": ["libs/e2e-lib/e2e-fee/src/index.ts"], "@e2e/lib/e2e-freeze-payment-form": ["libs/e2e-lib/e2e-freeze-payment-form/src/index.ts"], "@e2e/lib/e2e-identity-parties": ["libs/e2e-lib/e2e-identity-parties/src/index.ts"], "@e2e/lib/e2e-identity-parties-existing-application": ["libs/e2e-lib/e2e-identity-parties-existing-application/src/index.ts"], "@e2e/lib/e2e-identity-parties-form": ["libs/e2e-lib/e2e-identity-parties-form/src/index.ts"], "@e2e/lib/e2e-layout": ["libs/e2e-lib/e2e-layout/src/index.ts"], "@e2e/lib/e2e-life-insurance": ["libs/e2e-lib/e2e-life-insurance/src/index.ts"], "@e2e/lib/e2e-limit-details-form": ["libs/e2e-lib/e2e-limit-details-form/src/index.ts"], "@e2e/lib/e2e-limit-details-view": ["libs/e2e-lib/e2e-limit-details-view/src/index.ts"], "@e2e/lib/e2e-loan-change-amount-form": ["libs/e2e-lib/e2e-loan-change-amount-form/src/index.ts"], "@e2e/lib/e2e-loan-change-date-form": ["libs/e2e-lib/e2e-loan-change-date-form/src/index.ts"], "@e2e/lib/e2e-loan-details-preview": ["libs/e2e-lib/e2e-loan-details-preview/src/index.ts"], "@e2e/lib/e2e-loan-disbursement-form": ["libs/e2e-lib/e2e-loan-disbursement-form/src/index.ts"], "@e2e/lib/e2e-loan-effective-date-form": ["libs/e2e-lib/e2e-loan-effective-date-form/src/index.ts"], "@e2e/lib/e2e-loan-effective-date-view": ["libs/e2e-lib/e2e-loan-effective-date-view/src/index.ts"], "@e2e/lib/e2e-loan-header": ["libs/e2e-lib/e2e-loan-header/src/index.ts"], "@e2e/lib/e2e-loan-management-services": ["libs/e2e-lib/e2e-loan-management-services/src/index.ts"], "@e2e/lib/e2e-loan-upload-documents": ["libs/e2e-lib/e2e-loan-upload-documents/src/index.ts"], "@e2e/lib/e2e-loans-overdue-payment": ["libs/e2e-lib/e2e-loans-overdue-payment/src/index.ts"], "@e2e/lib/e2e-parties-view": ["libs/e2e-lib/e2e-parties-view/src/index.ts"], "@e2e/lib/e2e-pipes": ["libs/e2e-lib/e2e-pipes/src/index.ts"], "@e2e/lib/e2e-portfolio-collateral-form": ["libs/e2e-lib/e2e-portfolio-collateral-form/src/index.ts"], "@e2e/lib/e2e-portfolio-collaterals": ["libs/e2e-lib/e2e-portfolio-collaterals/src/index.ts"], "@e2e/lib/e2e-portfolio-form": ["libs/e2e-lib/e2e-portfolio-form/src/index.ts"], "@e2e/lib/e2e-portfolio-header": ["libs/e2e-lib/e2e-portfolio-header/src/index.ts"], "@e2e/lib/e2e-portfolio-lien-form": ["libs/e2e-lib/e2e-portfolio-lien-form/src/index.ts"], "@e2e/lib/e2e-portfolio-loans": ["libs/e2e-lib/e2e-portfolio-loans/src/index.ts"], "@e2e/lib/e2e-portfolio-payers": ["libs/e2e-lib/e2e-portfolio-payers/src/index.ts"], "@e2e/lib/e2e-portfolio-product": ["libs/e2e-lib/e2e-portfolio-product/src/index.ts"], "@e2e/lib/e2e-portfolio-tracks": ["libs/e2e-lib/e2e-portfolio-tracks/src/index.ts"], "@e2e/lib/e2e-portfolio-view": ["libs/e2e-lib/e2e-portfolio-view/src/index.ts"], "@e2e/lib/e2e-portfolios-details-layout": ["libs/e2e-lib/e2e-portfolios-details-layout/src/index.ts"], "@e2e/lib/e2e-portfolios-sort": ["libs/e2e-lib/e2e-portfolios-sort/src/index.ts"], "@e2e/lib/e2e-product-fee-info": ["libs/e2e-lib/e2e-product-fee-info/src/index.ts"], "@e2e/lib/e2e-product-form": ["libs/e2e-lib/e2e-product-form/src/index.ts"], "@e2e/lib/e2e-product-info": ["libs/e2e-lib/e2e-product-info/src/index.ts"], "@e2e/lib/e2e-product-view": ["libs/e2e-lib/e2e-product-view/src/index.ts"], "@e2e/lib/e2e-regulatory-reports": ["libs/e2e-lib/e2e-regulatory-reports/src/index.ts"], "@e2e/lib/e2e-repayments-simulation": ["libs/e2e-lib/e2e-repayments-simulation/src/index.ts"], "@e2e/lib/e2e-set-effective-date-form": ["libs/e2e-lib/e2e-set-effective-date-form/src/index.ts"], "@e2e/lib/e2e-simulate-repayment-preview": ["libs/e2e-lib/e2e-simulate-repayment-preview/src/index.ts"], "@e2e/lib/e2e-unfreeze-payment-form": ["libs/e2e-lib/e2e-unfreeze-payment-form/src/index.ts"], "@e2e/lib/filters-panel": ["libs/e2e-lib/filters-panel/src/index.ts"], "@e2e/lib/overdue-tooltip": ["libs/e2e-lib/e2e-overdue-tooltip/src/index.ts"], "@e2e/lib/print-and-download": ["libs/e2e-lib/print-and-download/src/index.ts"], "@e2e/lib/search-field": ["libs/e2e-lib/search-field/src/index.ts"], "@e2e/lib/services": ["libs/e2e-lib/services/src/index.ts"], "@e2e/lib/simulated-credit-limit-view": ["libs/e2e-lib/simulated-credit-limit-view/src/index.ts"], "@e2e/lib/simulated-portfolio-view": ["libs/e2e-lib/simulated-portfolio-view/src/index.ts"], "@e2e/lib/stacked-bar-chart": ["libs/e2e-lib/stacked-bar-chart/src/index.ts"], "@e2e/lib/state": ["libs/e2e-lib/state/src/index.ts"], "@e2e/lib/types": ["libs/e2e-lib/types/src/index.ts"], "@tenant-management/lib": ["libs/tenant-management-lib/src/index.ts"], "@tenant-management/lib/angular-material": ["libs/tenant-management-lib/angular-material/src/index.ts"], "@tenant-management/lib/authentication": ["libs/tenant-management-lib/authentication/src/index.ts"], "@tenant-management/lib/back-to-previous-page": ["libs/tenant-management-lib/back-to-previous-page/src/index.ts"], "@tenant-management/lib/control-value-accessor": ["libs/tenant-management-lib/control-value-accessor/src/index.ts"], "@tenant-management/lib/core": ["libs/tenant-management-lib/core/src/index.ts"], "@tenant-management/lib/directives": ["libs/tenant-management-lib/directives/src/index.ts"], "@tenant-management/lib/environments": ["libs/tenant-management-lib/environments/src/index.ts"], "@tenant-management/lib/footer": ["libs/tenant-management-lib/footer/src/index.ts"], "@tenant-management/lib/loader": ["libs/tenant-management-lib/loader/src/index.ts"], "@tenant-management/lib/main-layout": ["libs/tenant-management-lib/main-layout/src/index.ts"], "@tenant-management/lib/ngrx-store-localstorage": ["libs/tenant-management-lib/ngrx-store-localstorage/src/index.ts"], "@tenant-management/lib/ngx-document-viewer": ["libs/tenant-management-lib/ngx-document-viewer/src/index.ts"], "@tenant-management/lib/no-results": ["libs/tenant-management-lib/no-results/src/index.ts"], "@tenant-management/lib/partaker-chat": ["libs/tenant-management-lib/partaker-chat/src/index.ts"], "@tenant-management/lib/primeng": ["libs/tenant-management-lib/primeng/src/index.ts"], "@tenant-management/lib/refresh-token-confirmation": ["libs/tenant-management-lib/refresh-token-confirmation/src/index.ts"], "@tenant-management/lib/select-table-columns": ["libs/tenant-management-lib/select-table-columns/src/index.ts"], "@tenant-management/lib/services": ["libs/tenant-management-lib/services/src/index.ts"], "@tenant-management/lib/settings": ["libs/tenant-management-lib/settings/src/index.ts"], "@tenant-management/lib/snack-bar": ["libs/tenant-management-lib/snack-bar/src/index.ts"], "@tenant-management/lib/state": ["libs/tenant-management-lib/state/src/index.ts"], "@tenant-management/lib/toggle-with-text": ["libs/tenant-management-lib/toggle-with-text/src/index.ts"], "@tenant-management/lib/tooltip": ["libs/tenant-management-lib/tooltip/src/index.ts"], "@tenant-management/lib/types": ["libs/tenant-management-lib/types/src/index.ts"], "@tenant-management/lib/upload-documents": ["libs/tenant-management-lib/upload-documents/src/index.ts"], "@tenant-management/lib/validators": ["libs/tenant-management-lib/validators/src/index.ts"], "@ui/lib": ["libs/ui-lib/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}