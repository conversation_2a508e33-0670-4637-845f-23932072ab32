import { BorrowerDetail } from '@e2e/lib/types';
import { createAction, props } from '@ngrx/store';

export const getIdentityPartiesSuccess = createAction(
  '[Application credit limit early repayment parties API] Get identity parties Success',
  props<{ identityParties: BorrowerDetail[] }>()
);

export const getIdentityPartiesFailure = createAction(
  '[Application credit limit early repayment API] Get identity parties Failure',
  props<{ error: unknown }>()
);

export const getLimitEarlyRepaymentApplicationSuccess = createAction(
  '[Credit limit early repayment API] Get credit limit early repayment application Success',
  props<{ application: any; identityParties: any }>()
);

export const getLimitEarlyRepaymentApplicationFailure = createAction(
  '[Credit limit early repayment API] Get credit limit early repayment application Failure',
  props<{ error: unknown }>()
);

export const sendApplicationDecisionSuccess = createAction(
  '[Application credit limit early repayment decision maker API] Send application decision Success'
);

export const sendApplicationDecisionFailure = createAction(
  '[Application credit limit early repayment decision maker API] Send application decision Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitearlyRepaymentDetailsSuccess = createAction(
  '[Application credit limit early repayment details API] Get credit limit details Success',
  props<{ creditLimitOverdue: any }>()
);

export const getCreditLimitearlyRepaymentDetailsFailure = createAction(
  '[Application credit limit early repayment details API] Get credit limit details Failure',
  props<{ error: unknown }>()
);

export const getLoanPartialRepaymentsSimulationSuccess = createAction(
  '[Application credit limit early repayment details API] Get LoanPartialRepaymentsSimulationSuccess Success',
  props<{ loanSimulatedRepayment: any }>()
);

export const getLoanPartialRepaymentsSimulationFailure = createAction(
  '[Application credit limit early repayment details API] Get LoanPartialRepaymentsSimulationSuccess Failure',
  props<{ error: unknown }>()
);

export const getLoanDebtRepaymentsSuccess = createAction(
  '[Application credit limit early repayment details API] Get Loan Debt Simulated Repayments Success',
  props<{
    loanSimulatedRepayment: any;
  }>()
);

export const getLoanDebtRepaymentsFailure = createAction(
  '[Application credit limit early repayment details API] Get Loan Debt Simulated Repayments Failure',
  props<{ error: unknown }>()
);
