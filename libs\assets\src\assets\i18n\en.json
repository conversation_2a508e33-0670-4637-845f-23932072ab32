{"general": {"header": {"profile": "Profile", "tenantLogo": "Tenant logo"}, "navigation": {"routes": {"operations": {"title": "Operations", "applications": "Applications", "borrowers": "Borrowers", "loans": "Loans", "overview": "Overview", "orders": "Orders", "businessUnits": "Business Units", "creditLimits": "Credit limits", "paymentRequests": "Payment requests"}, "insights": "Insights", "referenceData": {"title": "Reference data", "anchors": "Anchors", "linkages": "Linkages"}, "productCatalog": {"title": "Product Catalog", "products": "Products", "productNew": "Product creation"}, "permissions": "Permissions", "userManagement": "User Management", "knowledge": "Knowledge", "cashflowSimulations": {"title": "Cash flow simulator", "simulatedLoans": "Simulated loans", "cashflowReports": "Cash flow report"}, "regulatoryReports": {"title": "Regulatory reports", "monthlyReport": "Monthly Report", "unusualReport": "Unusual report", "yearlyReporting": "Yearly reporting", "semiYearlyReporting": "Semi-yearly reporting", "reportCreditDataRepo": "Report to da Credit Data Repository"}, "configurations": {"title": "Configurations", "financialSources": "Financial sources", "financialMonths": "Financial months"}}}, "solutions": {"E2E": "E2E Loans", "HOME": "Home", "BNPL": "BNPL", "EWA": "Early Wage", "RISK": "Risk"}, "login": {"loginMsg": "Welcome to your Admin Portal"}, "buttons": {"language": "עברית", "confirm": "Confirm", "review": "Review", "reject": "Reject", "verify": "Verify", "approve": "Approve", "suspend": "Suspend merchant", "viewOrders": "View orders", "cancelEdit": "Cancel edit", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "save": "Save", "saveAndClose": "Save and close", "view": "View", "requestNew": "Request new", "sendRequest": "Send request", "filter": "Filter", "print": "Print", "download": "Download", "login": "<PERSON><PERSON>", "loginSso": "SSO Login", "logout": "Logout", "stayLoggedIn": "Stay logged in", "newApplication": "New Application", "cancelApplication": "Cancel Application", "goToApplication": "Go to Application", "retryApplication": "Retry Application", "retry": "Retry", "contactSupport": "Contact Support", "newDeal": "New Deal", "apply": "Apply", "reset": "Reset", "disburse": "Disburse", "productDetails": "Product details", "viewAllLoans": "View all loans", "continue": "Continue", "search": "Search", "createBorrower": "Create <PERSON><PERSON><PERSON>", "createOwner": "Create Owner", "addBorrower": "<PERSON><PERSON>", "addOwner": "Add Owner", "remove": "Remove", "createNewCollateral": "Create new Collateral", "removeCollateral": "remove Collateral", "addAnotherCollateral": "Add another Collateral", "addProductDetails": "Add Product details", "createParty": "Create Party", "addParty": "Add party", "addAnotherParty": "Add another Party", "removeParty": "Remove Party", "addAnotherConsumer": "Add another Beneficiary", "addConsumer": "Add Beneficiary", "createConsumer": "Create Beneficiary", "removeConsumer": "Remove Beneficiary", "createPortfolio": "Create Portfolio", "removePortfolio": "Remove Portfolio", "addAnotherPortfolio": "Add another Portfolio", "updatePortfolio": "Update Portfolio", "cancelLoan": "Cancel loan", "cancelLimit": "Cancel limit", "back": "Back", "keepEditing": "Keep editing", "create": "Create", "update": "Update", "sendForApproval": "Send for A<PERSON>roval", "addLien": "Add lien", "editDetails": "Edit details", "newCreditLimit": "New Credit Limit", "preview": "Preview", "editProductDetails": "Edit Product Details", "enable": "Enable", "disable": "Disable", "actions": "Actions", "portfolioActions": "Portfolio actions", "disbursement": "Disbursement", "changeEndUtilizationDate": "Change end utilization date", "setEffectiveDate": "Set effective date", "changeEffectiveDate": "Change effective date", "changeAmount": "Change amount", "simulateRepaymentTable": "Simulate repayment table", "submit": "Submit", "earlyRepayment": "Early repayment", "freezePayment": "Freeze payment", "unfreezePayment": "Unfreeze payment", "changePaymentMethod": "Change payment method", "changePaymentDate": "Change payment date", "createNewLimit": "Create limit", "changeLimitAmount": "Change Limit amount", "cancelLimitAmount": "Cancel Credit limit", "discard": "Discard", "viewLimit": "View Limit", "yes": "Yes", "no": "No", "details": "Details", "repay": "<PERSON>ay", "viewLoan": "View loan", "calculate": "Calculate", "simulateRepayments": "Simulate repayments", "validateDraft": "Validate draft", "createSimulatedLoan": "Create simulated loan", "goBack": "Go back", "deleteSimulation": "Delete simulation", "deleteSimulatedLoan": "Delete simulated loan", "close": "Close", "viewDetails": "View Details", "payers": "Payers", "newLinkageRate": "New Linkage Rate", "simulate": "Simulate", "paymentSchedule": "Payment schedule", "moreDetails": "More details", "returnToDraft": "Return to DRAFT", "viewSimulation": "View simulation", "letterOfIntent": "Letter of intent", "generate": "Generate"}, "footer": {"poweredBy": "Powered by "}, "snackBarMessages": {"submitLoanApplication": {"success": "The application has been submitted successfully."}, "loanEarlyRepayment": {"success": "Action successful."}, "loanChangePaymentDate": {"success": "Action successful."}, "default": {"success": "Action successful.", "failure": "Technical issue, please contact support team. Thank you for understanding."}}, "uploadDocuments": {"default": "Upload documents", "ID_CARD": "Upload ID card", "DRIVING_LICENSE": "Upload driving license", "PASSPORT": "Upload passport", "COMPANY_REGISTRATION": "Upload company registration", "COLLATERAL": "Upload collateral", "INSURANCE": "Upload insurance", "PORTFOLIO": "Upload portfolio documents", "LOAN": "Upload loan documents", "CREDIT_LIMIT": "Upload documents", "FREEZE_LOAN": "Upload documents", "UNFREEZE_LOAN": "Upload documents", "EARLY_REPAYMENT": "Upload documents", "CHANGE_PAYMENT_DAY": "Upload documents"}, "filestack": {"fileSizeLimit": "The file is over the limit"}, "creditTypes": {"LOAN": "Loan", "MORTGAGE": "Mortgage", "SUPPLIER_CREDIT": "Supplier credit", "CORPORATE": "Corporate"}, "errors": {"retryApplication": "Application process has failed due to technical issues. Please retry applying or get support from our team."}, "refreshTokenDialog": {"title": "Session expiration", "sessionMessage": "Due to <span class='text-xl font-bold'>{{ inactivityTime }}</span> minutes inactivity, your session will end after", "minutes": "minutes"}, "validationErrors": {"required": "The field is required.", "dateBetweenLimit": "Date should be within Limit availability period.", "dateEqualOrBeforeToday": "Date should be less or equal to Today.", "dateAfterToday": "Date should be greater than Today.", "dateAfterPortfolioEffectiveDate": "Date should be greater than Portfolio effective date.", "endUtilDateGreaterThenTheOldOne": "New end utilization date should be greater than the old one.", "dateLessOrEqualPortfolioMaturityDate": "Date should be less or equal to Portfolio maturity date.", "dateGreaterOrEqualEndUtilDate": "Date should be greater or equal to End utilization date.", "dateBetweenEffectiveDateAndEndUtilDate": "Date should be greater or equal to Portfolio effective date and less or equal to End utilization date.", "dateBeforeMaturityDate": "Date should be before Portfolio maturity date.", "dateGreaterOrEqualThenSignDate": "Date should be greater or equal to Contract signing date.", "paymentDate": "Repayment day should be on Portfolio payment day.", "interestBaseEffectiveDate": "Date should be from the provided list.", "amountBetweenZeroAndUnutilizedAmount": "Amount should be greater than 0 and less or equal to Portfolio unutilized amount.", "amountBetweenZeroAndLimitAmount": "Amount should be greater than 0 and less or equal to Limit available amount.", "amountGreaterThenPortfolioUtilizedAmount": "Amount should be greater or equal to Portfolio utilized amount.", "amountLessThenLimitAmount": "Amount should be less or equal to Limit available amount.", "paymentDay": "The day should be a number between 1 and 31.", "notEqualMaturityDates": "First repayment date is incorrect, loan maturity dates will not be equal.", "notEnoughAvailableAmount": "Not enough available amount for the selected Credit limit.", "aboveTheMaximum": "The amount is above the maximum amount.", "simulatedCreditLimit": "The simulated credit limit cannot be reduced by more than the available amount.", "simulatedPortfolioMin": "The simulated portfolio cannot be reduced by more than the unutilized amount.", "simulatedPortfolioMax": "The amount can't exceed the Limit available amount.", "portfolioUnutilizedMax": "The amount can't exceed the Portfolio unutilized amount.", "greaterThenZero": "The amount must be greater then 0.", "repaymentDateBeforeCreteDate": "First repayment date should be after the Creation date."}, "tableColumns": {"selectedColumnsCount": "Columns {{selected}} of {{all}}", "displayAll": "Display all"}, "actions": "Actions", "confirmAction": "Confirm action", "noResults": "No matching results", "no": "No", "noResultsFound": "No results found", "paginator": {"itemsPerPage": "Items per page:", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "range": "{{startIndex}} - {{endIndex}} of {{length}}"}, "loanCreditTypes": {"LOAN": "Loan", "MORTGAGE": "Mortgage", "SUPPLIER_CREDIT": "Supplier credit", "CORPORATE": "Corporate"}, "simulatedLoanTypes": {"REAL": "Real", "SIMULATED": "Simulated"}, "partakerChat": {"copy": "Copy", "newChatBtn": "New chat", "slogan": "AI that sets your business free", "ask": "Ask Spartakus"}, "true": "Yes", "false": "No"}}