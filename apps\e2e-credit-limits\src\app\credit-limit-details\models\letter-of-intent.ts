import {
  CreditLimitApplicationError,
  CreditLimitStatusChange,
  LoanApplicant,
} from '@e2e/lib/types';
import { Amount, TmDocument } from '@tenant-management/lib/types';

export interface LetterOfIntent {
  limitId: number;
  applicant: LoanApplicant;
  amount: Amount;
  valueDate: string;
  organizationName: string;
  id: string;
  type: string;
  status: string;
  statusReason: string;
  statusChanges: CreditLimitStatusChange[];
  documents: TmDocument[];
  error: CreditLimitApplicationError;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}
