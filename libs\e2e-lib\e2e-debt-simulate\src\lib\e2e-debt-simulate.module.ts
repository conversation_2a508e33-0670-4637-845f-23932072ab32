import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { E2ePipesModule } from '@e2e/lib/e2e-pipes';
import { TranslocoModule } from '@ngneat/transloco';
import { AngularMaterialModule } from '@tenant-management/lib/angular-material';
import { DebtSimulationComponent } from './debt-simulation/debt-simulation.component';

@NgModule({
  imports: [
    CommonModule,
    AngularMaterialModule,
    TranslocoModule,
    E2ePipesModule,
    ReactiveFormsModule,
  ],
  declarations: [DebtSimulationComponent],
  exports: [DebtSimulationComponent],
})
export class E2eDebtSimulateModule {}
