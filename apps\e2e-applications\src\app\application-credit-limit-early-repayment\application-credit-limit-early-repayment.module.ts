import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslocoModule, TRANSLOCO_SCOPE } from '@ngneat/transloco';

import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { E2eApplicationDetailsLayoutModule } from '@e2e/lib/e2e-application-details-layout';
import { E2eFeeModule } from '@e2e/lib/e2e-fee';
import { E2eLayoutModule } from '@e2e/lib/e2e-layout';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { TmStateSlices } from '@tenant-management/lib/types';

import { E2eOverdueTooltipModule } from '@e2e/lib/overdue-tooltip';
import { TooltipModule } from '@tenant-management/lib/tooltip';

import { E2eDebtSimulateModule } from '@e2e/lib/e2e-debt-simulate';
import { ApplicationCreditLimitEarlyRepaymentEffects } from './+state/application-credit-limit-early-repayment.effects';
import { ApplicationCreditLmitEarlyRepaymentReducer } from './+state/application-credit-limit-early-repayment.reducer';
import { ApplicationCreditLimiEarlyRepaymenttRoutingModule } from './application-credit-limit-early-repayment-routing.module';
import { ApplicationCreditLimitEarlyRepaymentLayoutComponent } from './components/application-credit-limit-early-repayment-layout/application-credit-limit-early-repayment-layout.component';
import { ApplicationCreditLimitEarlyRepaymentRequestComponent } from './components/application-credit-limit-early-repayment-request/application-credit-limit-early-repayment-request.component';
import { ApplicationCreditLimitEarlyRepaymentSimulationComponent } from './components/application-credit-limit-early-repayment-simulation/application-credit-limit-early-repayment-simulation.component';

@NgModule({
  declarations: [
    ApplicationCreditLimitEarlyRepaymentLayoutComponent,
    ApplicationCreditLimitEarlyRepaymentRequestComponent,
    ApplicationCreditLimitEarlyRepaymentSimulationComponent,
  ],
  imports: [
    CommonModule,
    TranslocoModule,
    ApplicationCreditLimiEarlyRepaymenttRoutingModule,
    E2eLayoutModule,
    E2eApplicationDetailsLayoutModule,
    MatExpansionModule,
    MatDividerModule,
    MatIconModule,
    MatButtonModule,
    E2eDebtSimulateModule,
    EffectsModule.forFeature([ApplicationCreditLimitEarlyRepaymentEffects]),
    StoreModule.forFeature(
      TmStateSlices.ApplicationCreditLimitEarlyRepayment,
      ApplicationCreditLmitEarlyRepaymentReducer
    ),
    E2eFeeModule,
    E2eOverdueTooltipModule,
    TooltipModule,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: [
        {
          scope: 'e2e/application-details',
          alias: 'application-details',
        },
        {
          scope: 'e2e/credit-limit-details',
          alias: 'credit-limit-details',
        },
        {
          scope: 'e2e/credit-limit-early-repayment',
          alias: 'credit-limit-early-repayment',
        },
        {
          scope: 'e2e',
          alias: 'e2e',
        },
      ],
    },
  ],
})
export class ApplicationCreditLimitEarlyRepaymentModule {}
