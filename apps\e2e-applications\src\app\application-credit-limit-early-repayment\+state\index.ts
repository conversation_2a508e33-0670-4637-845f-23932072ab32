import {
  ApplicationCreditLimitEarlyRepayment,
  ApplicationDetailsEarlyRepaymentDetails,
  ApplicationDetailsEarlyRepaymentLayout,
  ApplicationTypes,
  E2eEarlyRepaymentPortfolio,
  EarlyRepaymentTypes,
} from '@e2e/lib/types';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { TmStateSlices } from '@tenant-management/lib/types';
import { ApplicationCreditLimitEarlyRepaymentState } from './application-credit-limit-early-repayment.reducer';

export const selectCreditLimitEarlyRepaymentState =
  createFeatureSelector<ApplicationCreditLimitEarlyRepaymentState>(
    TmStateSlices.ApplicationCreditLimitEarlyRepayment
  );

export const selectCreditLimitEarlyRepApplicationLayout = createSelector(
  selectCreditLimitEarlyRepaymentState,
  (state) => {
    let applicationDetailsLayout: ApplicationDetailsEarlyRepaymentLayout | null =
      null;

    if (state) {
      const {
        id,
        approval,
        createdBy,
        status,
        error,
        createdAt,
        amount,
        creditLimit,
      } = state.application as ApplicationCreditLimitEarlyRepayment;

      applicationDetailsLayout = {
        id,
        approval,
        amount,
        decision: {
          applicationId: id,
          status,
          error,
        },
        overview: {
          createdOn: createdAt,
          applicationType: ApplicationTypes.CreditLimitEarlyRepayment,
          createdBy,
        },
        creditLimit,
        links: [
          {
            icon: 'icon-rotate-left',
            name: 'credit-limit-details.matTabLinks.request',
            url: './request',
            isVisible: true,
          },
          {
            icon: 'icon-cube-side',
            name: 'application-details.credit-limit-early-repayment.matTabLinks.simulation',
            url: './simulation',
            isVisible: true,
          },
        ],
      };
    }

    return applicationDetailsLayout;
  }
);

export const selectCreditLimitEarlyRepaymentDetails = createSelector(
  selectCreditLimitEarlyRepaymentState,
  (state) => {
    let applicationDetails: ApplicationDetailsEarlyRepaymentDetails | null =
      null;
    if (state.application && state.identityParties) {
      const {
        id,
        requestedBy,
        effectiveDate,
        creditLimit,
        amount,
        earlyRepaymentType,
        spreadMethod,
        fees,
        payers,
        portfolios,
        moneyAllocation,
      } = state.application as ApplicationCreditLimitEarlyRepayment;

      const borrower = state.identityParties.find(
        (party) => party.id === requestedBy.identityPartyId
      );
      const payerProp = payers ? payers[0] : null;
      const payer =
        payers && payerProp
          ? state.identityParties.find(
              (party) => party.id === payerProp.identityPartyId
            )
          : undefined;

      applicationDetails = {
        id,
        creditLimit,
        effectiveDate,
        requestedBy,
        amount,
        fees,
        payers: payerProp,
        portfolios,
        repaymentType: earlyRepaymentType,
        repaymentDistribution: spreadMethod,
        moneyAllocation,
        payer,
        identity: {
          ...borrower,
        },
      };
    }

    return applicationDetails;
  }
);

export const portfolios = createSelector(
  selectCreditLimitEarlyRepaymentDetails,
  (state) => {
    let selectedPortfolios: E2eEarlyRepaymentPortfolio[] = [];
    let totalDebtAmount = 0;
    if (state?.portfolios) {
      const { portfolios } = state;

      //selectedPortfolios = sumCreditLimitEarlyRepaymentPortfolios(portfolios); // TODO use this later
      selectedPortfolios = structuredClone(portfolios);
      for (let i = 0; i < selectedPortfolios.length; i++) {
        const portfolio = selectedPortfolios[i];
        let portfolioSum = 0;
        totalDebtAmount += portfolio.debtAmount;
        for (let k = 0; k < portfolio.tracks?.length; k++) {
          const track = portfolio.tracks[k];
          let trackSum = 0;
          for (let m = 0; m < track.loans?.length; m++) {
            const loan = track.loans[m];
            if (loan.earlyRepaymentType === EarlyRepaymentTypes.Partial) {
              portfolioSum += loan.earlyRepaymentAmount || 0;
              trackSum += loan.earlyRepaymentAmount || 0;
            }
            if (loan.earlyRepaymentType === EarlyRepaymentTypes.Full) {
              portfolioSum += loan.amountFullRepayment || 0;
              trackSum += loan.amountFullRepayment || 0;
            }
          }
          selectedPortfolios[i].tracks[k].earlyRepaymentAmount = trackSum;
        }
        selectedPortfolios[i].earlyRepaymentAmount = portfolioSum;
      }
    }

    return {
      ...state,
      totalDebtAmount,
      portfolios: selectedPortfolios,
      amountToRepay: state?.amount?.value ? +state?.amount?.value : 0,
    };
  }
);

export const selectApplicationId = createSelector(
  selectCreditLimitEarlyRepaymentState,
  (state) => {
    return state.applicationId;
  }
);

export const selectApplicationStatus = createSelector(
  selectCreditLimitEarlyRepaymentState,
  ({ application }) => application.status
);
