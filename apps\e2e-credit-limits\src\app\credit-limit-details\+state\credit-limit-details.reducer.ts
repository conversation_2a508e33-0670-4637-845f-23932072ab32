import { E2eLoanApiActions } from '@e2e/lib/state';
import {
  ApplicationProductPrepayments,
  BorrowerDetail,
  CreditLimitCollateral,
  CreditLimitEarlyRepayment,
  CreditLimitOwner,
  defaultPortfoliosSort,
  E2eBankNumber,
  E2eEarlyRepaymentOverview,
  E2eEarlyRepaymentPortfolio,
  E2eTotalOverdue,
  EarlyRepaymentTypes,
  LifeInsuranceCoverage,
  LoanEarlyRepaymentFee,
  LoanPortfolioDetails,
  Payers,
  PortfolioProductDetails,
  PortfolioShort,
  PortfoliosSort,
  sortPortfolios,
} from '@e2e/lib/types';
import { createReducer, on } from '@ngrx/store';
import { TenantManagementLibPageActions } from '@tenant-management/lib/state';
import {
  HttpState,
  SnackBarData,
  SnackBarMessageTypes,
} from '@tenant-management/lib/types';
import { CreditLimitDetails } from '../models/credit-limit-details';
import { CreditLimitPortfolioResponse } from '../models/credit-limit-portfolio-response';
import { CreditLimitsResponse } from '../models/credit-limits-response';
import { LetterOfIntent } from '../models/letter-of-intent';
import {
  CreditLimitDetailsApiActions,
  CreditLimitDetailsPageActions,
} from './actions';

export interface CreditLimitDetailsState {
  limitId: any;
  creditLimitCurrentPortfolioId: string;
  customerId: string;
  consumerIdentityIds: string[];
  creditLimits: HttpState<CreditLimitsResponse[]>;
  creditLimitDetails: CreditLimitDetails;
  creditLimitOwner: CreditLimitOwner;
  creditLimitConsumers: HttpState<BorrowerDetail[]>;
  creditLimitPortfolios: HttpState<CreditLimitPortfolioResponse>;
  lifeInsuranceCoverage: HttpState<LifeInsuranceCoverage>;
  creditLimitPortfolioDetails: LoanPortfolioDetails;
  creditLimitCollaterals: HttpState<CreditLimitCollateral[]>;
  collateralId: string;
  collateralOwnerId: string;
  snackBarMessage: SnackBarData;
  portfoliosSort: PortfoliosSort;
  prepayments: ApplicationProductPrepayments[] | null;
  productDetails: PortfolioProductDetails;
  selectedTrackId: string | undefined;
  currentProductTrackReferenceName: string;
  updatePayer: {
    payerUpdate: boolean;
    bankAccount: E2eBankNumber;
    payer: Payers;
  };
  fees: HttpState<LoanEarlyRepaymentFee[]>;
  overview: E2eEarlyRepaymentOverview;
  selectedPortfolios: {
    [id: string]: {
      portfolio: E2eEarlyRepaymentPortfolio;
      mode: EarlyRepaymentTypes | null;
    };
  };
  earlyRepaymentForm: CreditLimitEarlyRepayment;
  creditLimitOverdue: any;
  totalSelectedLoanAmount: any;
  totalOverdue: E2eTotalOverdue;
  simulationRequested: boolean;
  amountToRepay: string | number;
  isFormValid: boolean;
  letterOfIntent: LetterOfIntent;
}

export const initialState: CreditLimitDetailsState = {
  limitId: '',
  creditLimitCurrentPortfolioId: '',
  customerId: '',
  consumerIdentityIds: [],
  creditLimits: {} as HttpState<CreditLimitsResponse[]>,
  creditLimitDetails: {} as CreditLimitDetails,
  creditLimitOwner: {} as CreditLimitOwner,
  creditLimitConsumers: {} as HttpState<BorrowerDetail[]>,
  creditLimitPortfolios: {} as HttpState<CreditLimitPortfolioResponse>,
  lifeInsuranceCoverage: {} as HttpState<LifeInsuranceCoverage>,
  creditLimitPortfolioDetails: {} as LoanPortfolioDetails,
  creditLimitCollaterals: {} as HttpState<CreditLimitCollateral[]>,
  collateralId: '',
  collateralOwnerId: '',
  snackBarMessage: {} as SnackBarData,
  portfoliosSort: defaultPortfoliosSort,
  prepayments: null,
  productDetails: {} as PortfolioProductDetails,
  selectedTrackId: '',
  currentProductTrackReferenceName: '',
  updatePayer: {
    payerUpdate: false,
    bankAccount: {} as E2eBankNumber,
    payer: {} as Payers,
  },
  fees: {} as HttpState<LoanEarlyRepaymentFee[]>,
  overview: {} as E2eEarlyRepaymentOverview,
  selectedPortfolios: {} as {
    [id: string]: {
      portfolio: E2eEarlyRepaymentPortfolio;
      mode: EarlyRepaymentTypes | null;
    };
  },
  earlyRepaymentForm: {} as CreditLimitEarlyRepayment,
  creditLimitOverdue: {} as any,
  totalSelectedLoanAmount: '',
  totalOverdue: {} as E2eTotalOverdue,
  simulationRequested: false,
  amountToRepay: 0,
  isFormValid: false,
  letterOfIntent: {} as LetterOfIntent,
};

export const creditLimitDetailsReducer = createReducer(
  initialState,
  on(
    CreditLimitDetailsPageActions.resetCreditLimitEarlyRepaymentState,
    (state): CreditLimitDetailsState => {
      return {
        ...state,
        fees: {} as HttpState<LoanEarlyRepaymentFee[]>,
        overview: undefined as unknown as E2eEarlyRepaymentOverview,
        earlyRepaymentForm: undefined as unknown as CreditLimitEarlyRepayment,
        selectedPortfolios: {} as {
          [id: string]: {
            portfolio: E2eEarlyRepaymentPortfolio;
            mode: EarlyRepaymentTypes | null;
          };
        },
        creditLimitOverdue: {} as any,
        simulationRequested: false,
        amountToRepay: 0,
        isFormValid: false,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.setCreditLimitId,
    (state, { limitId }): CreditLimitDetailsState => {
      return {
        ...state,
        limitId,
        creditLimitCurrentPortfolioId: '',
        creditLimits: {} as HttpState<CreditLimitsResponse[]>,
        creditLimitDetails: {} as CreditLimitDetails,
        creditLimitOwner: {} as CreditLimitOwner,
        creditLimitConsumers: {} as HttpState<BorrowerDetail[]>,
        creditLimitPortfolios: {} as HttpState<CreditLimitPortfolioResponse>,
        creditLimitPortfolioDetails: {} as LoanPortfolioDetails,
        selectedPortfolios: {} as {
          [id: string]: {
            portfolio: E2eEarlyRepaymentPortfolio;
            mode: EarlyRepaymentTypes | null;
          };
        },
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCreditLimitDetailsSuccess,
    CreditLimitDetailsApiActions.sendLimitDetailsDocumentSuccess,
    (state, { creditLimitDetails }): CreditLimitDetailsState => {
      return {
        ...state,
        customerId: creditLimitDetails.customerId,
        consumerIdentityIds: creditLimitDetails.consumerIdentityIds,
        creditLimitDetails,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCreditLimitOwnerSuccess,
    (state, { creditLimitOwner }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitOwner,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.generateLetterOfIntentSuccess,
    (state, { letterOfIntent }): CreditLimitDetailsState => {
      return {
        ...state,
        letterOfIntent,
      };
    }
  ),

  on(
    CreditLimitDetailsApiActions.getCreditLimitConsumersSuccess,
    (state, { creditLimitConsumers }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitConsumers,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCreditLimitPortfoliosSuccess,
    (state, { creditLimitPortfolios }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitPortfolios,
      };
    }
  ),

  on(
    CreditLimitDetailsApiActions.getCreditLimitPortfolioDetailsSuccess,
    (state, { portfolioIndex, portfolioDetails }): CreditLimitDetailsState => {
      const updatedBorrowerPortfolioDetails = {
        ...state.creditLimitPortfolioDetails,
      };
      updatedBorrowerPortfolioDetails[portfolioIndex] = portfolioDetails;

      return {
        ...state,
        creditLimitPortfolioDetails: updatedBorrowerPortfolioDetails,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getPortfolioLimitSuccess,
    (state, { portfolioIndex, limit }): CreditLimitDetailsState => {
      const updatedCreditLimitPortfolioDetails = {
        ...state.creditLimitPortfolioDetails,
      };
      const updatedPortfolio = {
        ...updatedCreditLimitPortfolioDetails[portfolioIndex],
      };
      updatedPortfolio.limit = { ...limit };
      updatedCreditLimitPortfolioDetails[portfolioIndex] = updatedPortfolio;

      return {
        ...state,
        creditLimitPortfolioDetails: updatedCreditLimitPortfolioDetails,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.setCreditLimitCurrentPortfolioId,
    (state, { creditLimitCurrentPortfolioId }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitCurrentPortfolioId,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.getCreditLimitPortfolioDetails,
    (state, { portfolioId }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitCurrentPortfolioId: portfolioId,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.sendDisbursementSuccess,
    CreditLimitDetailsApiActions.sendChangeDateSuccess,
    CreditLimitDetailsApiActions.sendEffectiveDateSuccess,
    CreditLimitDetailsApiActions.sendChangePortfolioAmountSuccess,
    CreditLimitDetailsApiActions.sendChangeAmountSuccess,
    CreditLimitDetailsApiActions.sendChangeEffectiveDateSuccess,
    CreditLimitDetailsApiActions.getBorrowerPortfolioProductsSuccess,
    CreditLimitDetailsApiActions.submitCreditLimitEarlyRepaymentSuccess,
    E2eLoanApiActions.savePayerBankAccountValidateSuccess,
    (state): CreditLimitDetailsState => {
      return {
        ...state,
        snackBarMessage: {
          message: 'general.snackBarMessages.default.success',
          messageType: SnackBarMessageTypes.Success,
        },
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.sortPortfolios,
    (state, { portfoliosSort }): CreditLimitDetailsState => {
      const sortedPortfolios = structuredClone(
        state.creditLimitPortfolios.data?.portfolios
      );

      return {
        ...state,
        portfoliosSort,
        creditLimitPortfolios: {
          data: {
            limitId: state.limitId,
            portfolios: sortPortfolios(
              portfoliosSort,
              sortedPortfolios as PortfolioShort[]
            ),
          },
          loading: false,
        },
        creditLimitPortfolioDetails: {} as LoanPortfolioDetails,
      };
    }
  ),
  on(
    E2eLoanApiActions.sendPortfolioDefinitionDocumentSuccess,
    (state, { portfolioId, documents }): CreditLimitDetailsState => {
      const updatedCreditLimitPortfolioDetails = {
        ...state.creditLimitPortfolioDetails,
      };

      for (const key in updatedCreditLimitPortfolioDetails) {
        if (updatedCreditLimitPortfolioDetails[key].id === portfolioId) {
          const updatedPortfolio = {
            ...updatedCreditLimitPortfolioDetails[key],
          };

          updatedPortfolio.documents = [...documents];
          updatedCreditLimitPortfolioDetails[key] = updatedPortfolio;
        }
      }

      return {
        ...state,
        creditLimitPortfolioDetails: updatedCreditLimitPortfolioDetails,
      };
    }
  ),

  on(
    TenantManagementLibPageActions.clearState,
    (state): CreditLimitDetailsState => {
      return {
        ...state,
        portfoliosSort: defaultPortfoliosSort,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getBorrowerPortfolioPrepaymentsProductsSuccess,
    (state, { prepayments }): CreditLimitDetailsState => {
      return {
        ...state,
        prepayments,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getBorrowerPortfolioProductsSuccess,
    (state, { portfolioProducts }): CreditLimitDetailsState => {
      return {
        ...state,
        productDetails: {
          ...state.productDetails,
          portfolioProducts,
        },
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.getBorrowerPortfolioProducts,
    (
      state,
      { portfolioReferenceName, portfolioId }
    ): CreditLimitDetailsState => {
      return {
        ...state,
        productDetails: {
          ...state.productDetails,
          portfolioReferenceName,
          portfolioId,
        },
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCollateralsSuccess,
    (state, { creditLimitCollaterals }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitCollaterals,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.selectCollateral,
    (state, { collateralId }): CreditLimitDetailsState => {
      return {
        ...state,
        collateralId,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.selectCollateralOwner,
    (state, { collateralOwnerId }): CreditLimitDetailsState => {
      return {
        ...state,
        collateralOwnerId,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.getBorrowerPortfolioProducts,
    (state, { trackId }): CreditLimitDetailsState => {
      return {
        ...state,
        selectedTrackId: trackId,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCreditLimitOwnerInsuranceSuccess,
    (state, { lifeInsuranceCoverage }): CreditLimitDetailsState => {
      return {
        ...state,
        lifeInsuranceCoverage,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getCreditLimitOwnerInsuranceFailure,
    (state, { error }): CreditLimitDetailsState => {
      return {
        ...state,
        lifeInsuranceCoverage: {
          loading: false,
          error,
          data: {} as LifeInsuranceCoverage,
        },
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.getTrackLoans,
    (state, { productTrackReferenceName }): CreditLimitDetailsState => {
      return {
        ...state,
        currentProductTrackReferenceName: productTrackReferenceName,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getFeesSuccess,
    (state, { fees }): CreditLimitDetailsState => {
      return {
        ...state,
        fees,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getOverviewSuccess,
    (state, { overview }): CreditLimitDetailsState => {
      return {
        ...state,
        overview,
      };
    }
  ),

  // on(
  //   CreditLimitDetailsPageActions.selectPortfolio,
  //   (state, { portfolio, mode }) => ({
  //     ...state,
  //     selectedPortfolios: {
  //       ...state.selectedPortfolios,
  //       [portfolio.id]: {
  //         portfolio,
  //         mode: mode || null,
  //       },
  //     },
  //   })
  // ),
  on(
    CreditLimitDetailsPageActions.deselectPortfolio,
    (state, { portfolioId }) => {
      const newSelection = { ...state.selectedPortfolios };
      delete newSelection[portfolioId];
      return { ...state, selectedPortfolios: newSelection };
    }
  ),
  on(
    CreditLimitDetailsPageActions.changePortfolioMode,
    (state, { portfolioId, mode }) => {
      if (!state.selectedPortfolios[portfolioId]) {
        return state;
      }
      return {
        ...state,
        selectedPortfolios: {
          ...state.selectedPortfolios,
          [portfolioId]: {
            ...state.selectedPortfolios[portfolioId],
            mode,
          },
        },
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.setEarlyRepaymentForm,
    (state, { earlyRepaymentForm }): CreditLimitDetailsState => {
      return {
        ...state,
        earlyRepaymentForm,
      };
    }
  ),
  on(
    CreditLimitDetailsApiActions.getOverdueSuccess,
    (state, { creditLimitOverdue }): CreditLimitDetailsState => {
      return {
        ...state,
        creditLimitOverdue,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.updateTotalSelectedLoanAmount,
    (state, { amount }) => ({
      ...state,
      totalSelectedLoanAmount: amount,
    })
  ),

  // on(
  //   CreditLimitDetailsPageActions.selectPortfolio,
  //   (state, { portfolio, mode }): CreditLimitDetailsState => {
  //     const enrichedPortfolio: E2eEarlyRepaymentPortfolio = {
  //       ...portfolio,
  //       earlyRepaymentType: mode ?? null, // ⬅️ null е допустим
  //       allocatedDebtRepayment: 0,
  //       tracks: (portfolio.tracks || []).map((track) => ({
  //         ...track,
  //         earlyRepaymentType: mode ?? null, // ⬅️ null вместо ""
  //         payers: null,
  //         loans: (track.loans || []).map((loan) => ({
  //           ...loan,
  //           earlyRepaymentType: mode ?? null, // ⬅️ отново null
  //           earlyRepaymentAmount:
  //             mode === EarlyRepaymentTypes.Partial
  //               ? loan.earlyRepaymentAmount ?? 0
  //               : 0,
  //         })),
  //       })),
  //     };

  //     return {
  //       ...state,
  //       selectedPortfolios: {
  //         ...state.selectedPortfolios,
  //         [portfolio.id]: {
  //           portfolio: enrichedPortfolio,
  //           mode: mode ?? null,
  //         },
  //       },
  //     };
  //   }
  // ),
  // on(
  //   CreditLimitDetailsPageActions.selectPortfolio,
  //   (
  //     state,
  //     { portfolio, mode, trackStates = {}, loanStates = {} }
  //   ): CreditLimitDetailsState => {
  //     const enrichedPortfolio: E2eEarlyRepaymentPortfolio = {
  //       ...portfolio,
  //       earlyRepaymentType: mode ?? null,
  //       allocatedDebtRepayment: 0,
  //       tracks: (portfolio.tracks || []).map((track) => {
  //         const ts = trackStates[track.externalId];
  //         const trackMode = ts?.mode ?? null;

  //         return {
  //           ...track,
  //           earlyRepaymentType: trackMode,
  //           payers: null,
  //           loans: (track.loans || []).map((loan) => {
  //             const ls = loanStates[loan.id];
  //             const loanMode = ls?.mode ?? null;
  //             const loanAmount =
  //               loanMode === EarlyRepaymentTypes.Partial ? ls?.amount ?? 0 : 0;

  //             return {
  //               ...loan,
  //               earlyRepaymentType: loanMode,
  //               earlyRepaymentAmount: loanAmount,
  //             };
  //           }),
  //         };
  //       }),
  //     };

  //     return {
  //       ...state,
  //       selectedPortfolios: {
  //         ...state.selectedPortfolios,
  //         [portfolio.id]: {
  //           portfolio: enrichedPortfolio,
  //           mode: mode ?? null,
  //         },
  //       },
  //     };
  //   }
  // ),

  on(
    CreditLimitDetailsPageActions.selectPortfolio,
    (
      state,
      { portfolio, mode, trackStates, loanStates }
    ): CreditLimitDetailsState => {
      const isFull = mode === EarlyRepaymentTypes.Full;

      const enrichedPortfolio: E2eEarlyRepaymentPortfolio = {
        ...portfolio,
        earlyRepaymentType: mode ?? null,
        allocatedDebtRepayment: 0,
        tracks: (portfolio.tracks || []).map((track) => ({
          ...track,
          earlyRepaymentType: isFull ? EarlyRepaymentTypes.Full : null,
          payers: [],
          loans: (track.loans || []).map((loan) => ({
            ...loan,
            earlyRepaymentType: isFull
              ? EarlyRepaymentTypes.Full
              : loanStates?.[loan.id]?.mode ?? null,
            earlyRepaymentAmount:
              loanStates?.[loan.id]?.mode === EarlyRepaymentTypes.Partial
                ? loanStates?.[loan.id]?.amount ?? 0
                : 0,
          })),
        })),
      };

      return {
        ...state,
        selectedPortfolios: {
          ...state.selectedPortfolios,
          [portfolio.id]: {
            portfolio: enrichedPortfolio,
            mode: mode ?? null,
          },
        },
      };
    }
  ),

  on(
    CreditLimitDetailsPageActions.updatePortfolioMode,
    (state, { portfolioId, mode }) => {
      const entry = state.selectedPortfolios[portfolioId];
      if (!entry) return state;

      return {
        ...state,
        selectedPortfolios: {
          ...state.selectedPortfolios,
          [portfolioId]: {
            ...entry,
            portfolio: {
              ...entry.portfolio,
              earlyRepaymentType: mode,
            },
            mode,
          },
        },
      };
    }
  ),

  on(
    CreditLimitDetailsPageActions.updateTrackMode,
    (state, { portfolioId, trackId, mode }) => {
      const entry = state.selectedPortfolios[portfolioId];
      if (!entry) return state;

      const updatedTracks = entry.portfolio.tracks.map((track) =>
        track.externalId === trackId
          ? { ...track, earlyRepaymentType: mode }
          : track
      );

      return {
        ...state,
        selectedPortfolios: {
          ...state.selectedPortfolios,
          [portfolioId]: {
            ...entry,
            portfolio: {
              ...entry.portfolio,
              tracks: updatedTracks,
            },
          },
        },
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.updateLoanModeAndAmount,
    (state, { portfolioId, trackId, loanId, mode, amount }) => {
      const entry = state.selectedPortfolios[portfolioId];
      if (!entry) return state;
      const updatedTracks = entry.portfolio.tracks.map((track) => {
        if (track.externalId !== trackId) return track;

        const updatedLoans = track.loans.map((loan) =>
          loan.id === loanId
            ? {
                ...loan,
                earlyRepaymentType: mode,
                earlyRepaymentAmount:
                  mode === EarlyRepaymentTypes.Partial ? amount ?? 0 : 0,
              }
            : loan
        );
        return {
          ...track,
          loans: updatedLoans,
        };
      });

      return {
        ...state,
        selectedPortfolios: {
          ...state.selectedPortfolios,
          [portfolioId]: {
            ...entry,
            portfolio: {
              ...entry.portfolio,
              tracks: updatedTracks,
            },
          },
        },
      };
    }
  ),

  on(
    CreditLimitDetailsPageActions.triggerSimulationRequest,
    (state): CreditLimitDetailsState => ({
      ...state,
      simulationRequested: true,
    })
  ),
  on(
    CreditLimitDetailsPageActions.clearSimulationRequest,
    (state): CreditLimitDetailsState => ({
      ...state,
      simulationRequested: false,
    })
  ),
  on(
    CreditLimitDetailsPageActions.setAmountToRepay,
    (state, { amount }): CreditLimitDetailsState => {
      return {
        ...state,
        amountToRepay: amount,
      };
    }
  ),
  on(
    CreditLimitDetailsPageActions.setIsFormValid,
    (state, { isFormValid }): CreditLimitDetailsState => {
      return {
        ...state,
        isFormValid,
      };
    }
  )
);
